<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>tinytroupe.tools API documentation</title>
<meta name="description" content="Tools allow agents to accomplish specialized tasks." />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>tinytroupe.tools</code></h1>
</header>
<section id="section-intro">
<p>Tools allow agents to accomplish specialized tasks.</p>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">&#34;&#34;&#34;
Tools allow agents to accomplish specialized tasks.
&#34;&#34;&#34;
import textwrap
import json
import copy

import logging
logger = logging.getLogger(&#34;tinytroupe&#34;)

import tinytroupe.utils as utils
from tinytroupe.extraction import ArtifactExporter
from tinytroupe.enrichment import TinyEnricher
from tinytroupe.utils import JsonSerializableRegistry


class TinyTool(JsonSerializableRegistry):

    def __init__(self, name, description, owner=None, real_world_side_effects=False, exporter=None, enricher=None):
        &#34;&#34;&#34;
        Initialize a new tool.

        Args:
            name (str): The name of the tool.
            description (str): A brief description of the tool.
            owner (str): The agent that owns the tool. If None, the tool can be used by anyone.
            real_world_side_effects (bool): Whether the tool has real-world side effects. That is to say, if it has the potential to change the 
                state of the world outside of the simulation. If it does, it should be used with caution.
            exporter (ArtifactExporter): An exporter that can be used to export the results of the tool&#39;s actions. If None, the tool will not be able to export results.
            enricher (Enricher): An enricher that can be used to enrich the results of the tool&#39;s actions. If None, the tool will not be able to enrich results.
        
        &#34;&#34;&#34;
        self.name = name
        self.description = description
        self.owner = owner
        self.real_world_side_effects = real_world_side_effects
        self.exporter = exporter
        self.enricher = enricher

    def _process_action(self, agent, action: dict) -&gt; bool:
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)
    
    def _protect_real_world(self):
        if self.real_world_side_effects:
            logger.warning(f&#34; !!!!!!!!!! Tool {self.name} has REAL-WORLD SIDE EFFECTS. This is NOT just a simulation. Use with caution. !!!!!!!!!!&#34;)
        
    def _enforce_ownership(self, agent):
        if self.owner is not None and agent.name != self.owner.name:
            raise ValueError(f&#34;Agent {agent.name} does not own tool {self.name}, which is owned by {self.owner.name}.&#34;)
    
    def set_owner(self, owner):
        self.owner = owner

    def actions_definitions_prompt(self) -&gt; str:
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)
    
    def actions_constraints_prompt(self) -&gt; str:
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def process_action(self, agent, action: dict) -&gt; bool:
        self._protect_real_world()
        self._enforce_ownership(agent)
        self._process_action(agent, action)


# TODO under development
class TinyCalendar(TinyTool):

    def __init__(self, owner=None):
        super().__init__(&#34;calendar&#34;, &#34;A basic calendar tool that allows agents to keep track meetings and appointments.&#34;, owner=owner, real_world_side_effects=False)
        
        # maps date to list of events. Each event itself is a dictionary with keys &#34;title&#34;, &#34;description&#34;, &#34;owner&#34;, &#34;mandatory_attendees&#34;, &#34;optional_attendees&#34;, &#34;start_time&#34;, &#34;end_time&#34;
        self.calenar = {}
    
    def add_event(self, date, title, description=None, owner=None, mandatory_attendees=None, optional_attendees=None, start_time=None, end_time=None):
        if date not in self.calendar:
            self.calendar[date] = []
        self.calendar[date].append({&#34;title&#34;: title, &#34;description&#34;: description, &#34;owner&#34;: owner, &#34;mandatory_attendees&#34;: mandatory_attendees, &#34;optional_attendees&#34;: optional_attendees, &#34;start_time&#34;: start_time, &#34;end_time&#34;: end_time})
    
    def find_events(self, year, month, day, hour=None, minute=None):
        # TODO
        pass

    def _process_action(self, agent, action) -&gt; bool:
        if action[&#39;type&#39;] == &#34;CREATE_EVENT&#34; and action[&#39;content&#39;] is not None:
            # parse content json
            event_content = json.loads(action[&#39;content&#39;])
            
            # checks whether there are any kwargs that are not valid
            valid_keys = [&#34;title&#34;, &#34;description&#34;, &#34;mandatory_attendees&#34;, &#34;optional_attendees&#34;, &#34;start_time&#34;, &#34;end_time&#34;]
            utils.check_valid_fields(event_content, valid_keys)

            # uses the kwargs to create a new event
            self.add_event(event_content)

            return True

        else:
            return False

    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
              - CREATE_EVENT: You can create a new event in your calendar. The content of the event has many fields, and you should use a JSON format to specify them. Here are the possible fields:
                * title: The title of the event. Mandatory.
                * description: A brief description of the event. Optional.
                * mandatory_attendees: A list of agent names who must attend the event. Optional.
                * optional_attendees: A list of agent names who are invited to the event, but are not required to attend. Optional.
                * start_time: The start time of the event. Optional.
                * end_time: The end time of the event. Optional.
            &#34;&#34;&#34;
        # TODO how the atendee list will be handled? How will they be notified of the invitation? I guess they must also have a calendar themselves. &lt;-------------------------------------

        return utils.dedent(prompt)
        
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
              
            &#34;&#34;&#34;
            # TODO

        return textwrap.dedent(prompt)
    


class TinyWordProcessor(TinyTool):

    def __init__(self, owner=None, exporter=None, enricher=None):
        super().__init__(&#34;wordprocessor&#34;, &#34;A basic word processor tool that allows agents to write documents.&#34;, owner=owner, real_world_side_effects=False, exporter=exporter, enricher=enricher)
        
    def write_document(self, title, content, author=None):
        logger.debug(f&#34;Writing document with title {title} and content: {content}&#34;)

        if self.enricher is not None:
            requirements =\
            &#34;&#34;&#34;
            Turn any draft or outline into an actual and long document, with many, many details. Include tables, lists, and other elements.
            The result **MUST** be at least 5 times larger than the original content in terms of characters - do whatever it takes to make it this long and detailed.
            &#34;&#34;&#34;
                
            content = self.enricher.enrich_content(requirements=requirements, 
                                                    content=content, 
                                                    content_type=&#34;Document&#34;, 
                                                    context_info=None,
                                                    context_cache=None, verbose=False)    
            
        if self.exporter is not None:
            self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= content, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;md&#34;)
            self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= content, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;docx&#34;)

            json_doc = {&#34;title&#34;: title, &#34;content&#34;: content, &#34;author&#34;: author}
            self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= json_doc, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;json&#34;)

    def _process_action(self, agent, action) -&gt; bool:
        try:
            if action[&#39;type&#39;] == &#34;WRITE_DOCUMENT&#34; and action[&#39;content&#39;] is not None:
                # parse content json
                if isinstance(action[&#39;content&#39;], str):
                    doc_spec = json.loads(action[&#39;content&#39;])  
                else:
                    doc_spec = action[&#39;content&#39;]
                
                # checks whether there are any kwargs that are not valid
                valid_keys = [&#34;title&#34;, &#34;content&#34;, &#34;author&#34;]
                utils.check_valid_fields(doc_spec, valid_keys)

                # uses the kwargs to create a new document
                self.write_document(**doc_spec)

                return True

            else:
                return False
        except json.JSONDecodeError as e:
            logger.error(f&#34;Error parsing JSON content: {e}. Original content: {action[&#39;content&#39;]}&#34;)
            return False

    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
            - WRITE_DOCUMENT: you can create a new document. The content of the document has many fields, and you should use a JSON format to specify them. Here are the possible fields:
                * title: The title of the document. Mandatory.
                * content: The actual content of the document. You **must** use Markdown to format this content. Mandatory.
                * author: The author of the document. You should put your own name. Optional.
            &#34;&#34;&#34;
        return utils.dedent(prompt)
        
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
            - Whenever you WRITE_DOCUMENT, you write all the content at once. Moreover, the content should be long and detailed, unless there&#39;s a good reason for it not to be.
            - When you WRITE_DOCUMENT, you follow these additional guidelines:
                * For any milestones or timelines mentioned, try mentioning specific owners or partner teams, unless there&#39;s a good reason not to do so.
            &#34;&#34;&#34;
        return utils.dedent(prompt)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="tinytroupe.tools.TinyCalendar"><code class="flex name class">
<span>class <span class="ident">TinyCalendar</span></span>
<span>(</span><span>owner=None)</span>
</code></dt>
<dd>
<div class="desc"><p>A mixin class that provides JSON serialization, deserialization, and subclass registration.</p>
<p>Initialize a new tool.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the tool.</dd>
<dt><strong><code>description</code></strong> :&ensp;<code>str</code></dt>
<dd>A brief description of the tool.</dd>
<dt><strong><code>owner</code></strong> :&ensp;<code>str</code></dt>
<dd>The agent that owns the tool. If None, the tool can be used by anyone.</dd>
<dt><strong><code>real_world_side_effects</code></strong> :&ensp;<code>bool</code></dt>
<dd>Whether the tool has real-world side effects. That is to say, if it has the potential to change the
state of the world outside of the simulation. If it does, it should be used with caution.</dd>
<dt><strong><code>exporter</code></strong> :&ensp;<code>ArtifactExporter</code></dt>
<dd>An exporter that can be used to export the results of the tool's actions. If None, the tool will not be able to export results.</dd>
<dt><strong><code>enricher</code></strong> :&ensp;<code>Enricher</code></dt>
<dd>An enricher that can be used to enrich the results of the tool's actions. If None, the tool will not be able to enrich results.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyCalendar(TinyTool):

    def __init__(self, owner=None):
        super().__init__(&#34;calendar&#34;, &#34;A basic calendar tool that allows agents to keep track meetings and appointments.&#34;, owner=owner, real_world_side_effects=False)
        
        # maps date to list of events. Each event itself is a dictionary with keys &#34;title&#34;, &#34;description&#34;, &#34;owner&#34;, &#34;mandatory_attendees&#34;, &#34;optional_attendees&#34;, &#34;start_time&#34;, &#34;end_time&#34;
        self.calenar = {}
    
    def add_event(self, date, title, description=None, owner=None, mandatory_attendees=None, optional_attendees=None, start_time=None, end_time=None):
        if date not in self.calendar:
            self.calendar[date] = []
        self.calendar[date].append({&#34;title&#34;: title, &#34;description&#34;: description, &#34;owner&#34;: owner, &#34;mandatory_attendees&#34;: mandatory_attendees, &#34;optional_attendees&#34;: optional_attendees, &#34;start_time&#34;: start_time, &#34;end_time&#34;: end_time})
    
    def find_events(self, year, month, day, hour=None, minute=None):
        # TODO
        pass

    def _process_action(self, agent, action) -&gt; bool:
        if action[&#39;type&#39;] == &#34;CREATE_EVENT&#34; and action[&#39;content&#39;] is not None:
            # parse content json
            event_content = json.loads(action[&#39;content&#39;])
            
            # checks whether there are any kwargs that are not valid
            valid_keys = [&#34;title&#34;, &#34;description&#34;, &#34;mandatory_attendees&#34;, &#34;optional_attendees&#34;, &#34;start_time&#34;, &#34;end_time&#34;]
            utils.check_valid_fields(event_content, valid_keys)

            # uses the kwargs to create a new event
            self.add_event(event_content)

            return True

        else:
            return False

    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
              - CREATE_EVENT: You can create a new event in your calendar. The content of the event has many fields, and you should use a JSON format to specify them. Here are the possible fields:
                * title: The title of the event. Mandatory.
                * description: A brief description of the event. Optional.
                * mandatory_attendees: A list of agent names who must attend the event. Optional.
                * optional_attendees: A list of agent names who are invited to the event, but are not required to attend. Optional.
                * start_time: The start time of the event. Optional.
                * end_time: The end time of the event. Optional.
            &#34;&#34;&#34;
        # TODO how the atendee list will be handled? How will they be notified of the invitation? I guess they must also have a calendar themselves. &lt;-------------------------------------

        return utils.dedent(prompt)
        
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
              
            &#34;&#34;&#34;
            # TODO

        return textwrap.dedent(prompt)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.tools.TinyTool" href="#tinytroupe.tools.TinyTool">TinyTool</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.tools.TinyCalendar.actions_constraints_prompt"><code class="name flex">
<span>def <span class="ident">actions_constraints_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_constraints_prompt(self) -&gt; str:
    prompt = \
        &#34;&#34;&#34;
          
        &#34;&#34;&#34;
        # TODO

    return textwrap.dedent(prompt)</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyCalendar.actions_definitions_prompt"><code class="name flex">
<span>def <span class="ident">actions_definitions_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_definitions_prompt(self) -&gt; str:
    prompt = \
        &#34;&#34;&#34;
          - CREATE_EVENT: You can create a new event in your calendar. The content of the event has many fields, and you should use a JSON format to specify them. Here are the possible fields:
            * title: The title of the event. Mandatory.
            * description: A brief description of the event. Optional.
            * mandatory_attendees: A list of agent names who must attend the event. Optional.
            * optional_attendees: A list of agent names who are invited to the event, but are not required to attend. Optional.
            * start_time: The start time of the event. Optional.
            * end_time: The end time of the event. Optional.
        &#34;&#34;&#34;
    # TODO how the atendee list will be handled? How will they be notified of the invitation? I guess they must also have a calendar themselves. &lt;-------------------------------------

    return utils.dedent(prompt)</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyCalendar.add_event"><code class="name flex">
<span>def <span class="ident">add_event</span></span>(<span>self, date, title, description=None, owner=None, mandatory_attendees=None, optional_attendees=None, start_time=None, end_time=None)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_event(self, date, title, description=None, owner=None, mandatory_attendees=None, optional_attendees=None, start_time=None, end_time=None):
    if date not in self.calendar:
        self.calendar[date] = []
    self.calendar[date].append({&#34;title&#34;: title, &#34;description&#34;: description, &#34;owner&#34;: owner, &#34;mandatory_attendees&#34;: mandatory_attendees, &#34;optional_attendees&#34;: optional_attendees, &#34;start_time&#34;: start_time, &#34;end_time&#34;: end_time})</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyCalendar.find_events"><code class="name flex">
<span>def <span class="ident">find_events</span></span>(<span>self, year, month, day, hour=None, minute=None)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def find_events(self, year, month, day, hour=None, minute=None):
    # TODO
    pass</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.tools.TinyTool" href="#tinytroupe.tools.TinyTool">TinyTool</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.tools.TinyTool.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.tools.TinyTool.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.tools.TinyTool"><code class="flex name class">
<span>class <span class="ident">TinyTool</span></span>
<span>(</span><span>name, description, owner=None, real_world_side_effects=False, exporter=None, enricher=None)</span>
</code></dt>
<dd>
<div class="desc"><p>A mixin class that provides JSON serialization, deserialization, and subclass registration.</p>
<p>Initialize a new tool.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the tool.</dd>
<dt><strong><code>description</code></strong> :&ensp;<code>str</code></dt>
<dd>A brief description of the tool.</dd>
<dt><strong><code>owner</code></strong> :&ensp;<code>str</code></dt>
<dd>The agent that owns the tool. If None, the tool can be used by anyone.</dd>
<dt><strong><code>real_world_side_effects</code></strong> :&ensp;<code>bool</code></dt>
<dd>Whether the tool has real-world side effects. That is to say, if it has the potential to change the
state of the world outside of the simulation. If it does, it should be used with caution.</dd>
<dt><strong><code>exporter</code></strong> :&ensp;<code>ArtifactExporter</code></dt>
<dd>An exporter that can be used to export the results of the tool's actions. If None, the tool will not be able to export results.</dd>
<dt><strong><code>enricher</code></strong> :&ensp;<code>Enricher</code></dt>
<dd>An enricher that can be used to enrich the results of the tool's actions. If None, the tool will not be able to enrich results.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyTool(JsonSerializableRegistry):

    def __init__(self, name, description, owner=None, real_world_side_effects=False, exporter=None, enricher=None):
        &#34;&#34;&#34;
        Initialize a new tool.

        Args:
            name (str): The name of the tool.
            description (str): A brief description of the tool.
            owner (str): The agent that owns the tool. If None, the tool can be used by anyone.
            real_world_side_effects (bool): Whether the tool has real-world side effects. That is to say, if it has the potential to change the 
                state of the world outside of the simulation. If it does, it should be used with caution.
            exporter (ArtifactExporter): An exporter that can be used to export the results of the tool&#39;s actions. If None, the tool will not be able to export results.
            enricher (Enricher): An enricher that can be used to enrich the results of the tool&#39;s actions. If None, the tool will not be able to enrich results.
        
        &#34;&#34;&#34;
        self.name = name
        self.description = description
        self.owner = owner
        self.real_world_side_effects = real_world_side_effects
        self.exporter = exporter
        self.enricher = enricher

    def _process_action(self, agent, action: dict) -&gt; bool:
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)
    
    def _protect_real_world(self):
        if self.real_world_side_effects:
            logger.warning(f&#34; !!!!!!!!!! Tool {self.name} has REAL-WORLD SIDE EFFECTS. This is NOT just a simulation. Use with caution. !!!!!!!!!!&#34;)
        
    def _enforce_ownership(self, agent):
        if self.owner is not None and agent.name != self.owner.name:
            raise ValueError(f&#34;Agent {agent.name} does not own tool {self.name}, which is owned by {self.owner.name}.&#34;)
    
    def set_owner(self, owner):
        self.owner = owner

    def actions_definitions_prompt(self) -&gt; str:
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)
    
    def actions_constraints_prompt(self) -&gt; str:
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def process_action(self, agent, action: dict) -&gt; bool:
        self._protect_real_world()
        self._enforce_ownership(agent)
        self._process_action(agent, action)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="tinytroupe.tools.TinyCalendar" href="#tinytroupe.tools.TinyCalendar">TinyCalendar</a></li>
<li><a title="tinytroupe.tools.TinyWordProcessor" href="#tinytroupe.tools.TinyWordProcessor">TinyWordProcessor</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.tools.TinyTool.actions_constraints_prompt"><code class="name flex">
<span>def <span class="ident">actions_constraints_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_constraints_prompt(self) -&gt; str:
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyTool.actions_definitions_prompt"><code class="name flex">
<span>def <span class="ident">actions_definitions_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_definitions_prompt(self) -&gt; str:
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyTool.process_action"><code class="name flex">
<span>def <span class="ident">process_action</span></span>(<span>self, agent, action: dict) ‑> bool</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def process_action(self, agent, action: dict) -&gt; bool:
    self._protect_real_world()
    self._enforce_ownership(agent)
    self._process_action(agent, action)</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyTool.set_owner"><code class="name flex">
<span>def <span class="ident">set_owner</span></span>(<span>self, owner)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def set_owner(self, owner):
    self.owner = owner</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.tools.TinyWordProcessor"><code class="flex name class">
<span>class <span class="ident">TinyWordProcessor</span></span>
<span>(</span><span>owner=None, exporter=None, enricher=None)</span>
</code></dt>
<dd>
<div class="desc"><p>A mixin class that provides JSON serialization, deserialization, and subclass registration.</p>
<p>Initialize a new tool.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the tool.</dd>
<dt><strong><code>description</code></strong> :&ensp;<code>str</code></dt>
<dd>A brief description of the tool.</dd>
<dt><strong><code>owner</code></strong> :&ensp;<code>str</code></dt>
<dd>The agent that owns the tool. If None, the tool can be used by anyone.</dd>
<dt><strong><code>real_world_side_effects</code></strong> :&ensp;<code>bool</code></dt>
<dd>Whether the tool has real-world side effects. That is to say, if it has the potential to change the
state of the world outside of the simulation. If it does, it should be used with caution.</dd>
<dt><strong><code>exporter</code></strong> :&ensp;<code>ArtifactExporter</code></dt>
<dd>An exporter that can be used to export the results of the tool's actions. If None, the tool will not be able to export results.</dd>
<dt><strong><code>enricher</code></strong> :&ensp;<code>Enricher</code></dt>
<dd>An enricher that can be used to enrich the results of the tool's actions. If None, the tool will not be able to enrich results.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyWordProcessor(TinyTool):

    def __init__(self, owner=None, exporter=None, enricher=None):
        super().__init__(&#34;wordprocessor&#34;, &#34;A basic word processor tool that allows agents to write documents.&#34;, owner=owner, real_world_side_effects=False, exporter=exporter, enricher=enricher)
        
    def write_document(self, title, content, author=None):
        logger.debug(f&#34;Writing document with title {title} and content: {content}&#34;)

        if self.enricher is not None:
            requirements =\
            &#34;&#34;&#34;
            Turn any draft or outline into an actual and long document, with many, many details. Include tables, lists, and other elements.
            The result **MUST** be at least 5 times larger than the original content in terms of characters - do whatever it takes to make it this long and detailed.
            &#34;&#34;&#34;
                
            content = self.enricher.enrich_content(requirements=requirements, 
                                                    content=content, 
                                                    content_type=&#34;Document&#34;, 
                                                    context_info=None,
                                                    context_cache=None, verbose=False)    
            
        if self.exporter is not None:
            self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= content, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;md&#34;)
            self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= content, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;docx&#34;)

            json_doc = {&#34;title&#34;: title, &#34;content&#34;: content, &#34;author&#34;: author}
            self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= json_doc, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;json&#34;)

    def _process_action(self, agent, action) -&gt; bool:
        try:
            if action[&#39;type&#39;] == &#34;WRITE_DOCUMENT&#34; and action[&#39;content&#39;] is not None:
                # parse content json
                if isinstance(action[&#39;content&#39;], str):
                    doc_spec = json.loads(action[&#39;content&#39;])  
                else:
                    doc_spec = action[&#39;content&#39;]
                
                # checks whether there are any kwargs that are not valid
                valid_keys = [&#34;title&#34;, &#34;content&#34;, &#34;author&#34;]
                utils.check_valid_fields(doc_spec, valid_keys)

                # uses the kwargs to create a new document
                self.write_document(**doc_spec)

                return True

            else:
                return False
        except json.JSONDecodeError as e:
            logger.error(f&#34;Error parsing JSON content: {e}. Original content: {action[&#39;content&#39;]}&#34;)
            return False

    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
            - WRITE_DOCUMENT: you can create a new document. The content of the document has many fields, and you should use a JSON format to specify them. Here are the possible fields:
                * title: The title of the document. Mandatory.
                * content: The actual content of the document. You **must** use Markdown to format this content. Mandatory.
                * author: The author of the document. You should put your own name. Optional.
            &#34;&#34;&#34;
        return utils.dedent(prompt)
        
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
            - Whenever you WRITE_DOCUMENT, you write all the content at once. Moreover, the content should be long and detailed, unless there&#39;s a good reason for it not to be.
            - When you WRITE_DOCUMENT, you follow these additional guidelines:
                * For any milestones or timelines mentioned, try mentioning specific owners or partner teams, unless there&#39;s a good reason not to do so.
            &#34;&#34;&#34;
        return utils.dedent(prompt)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.tools.TinyTool" href="#tinytroupe.tools.TinyTool">TinyTool</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.tools.TinyWordProcessor.actions_constraints_prompt"><code class="name flex">
<span>def <span class="ident">actions_constraints_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_constraints_prompt(self) -&gt; str:
    prompt = \
        &#34;&#34;&#34;
        - Whenever you WRITE_DOCUMENT, you write all the content at once. Moreover, the content should be long and detailed, unless there&#39;s a good reason for it not to be.
        - When you WRITE_DOCUMENT, you follow these additional guidelines:
            * For any milestones or timelines mentioned, try mentioning specific owners or partner teams, unless there&#39;s a good reason not to do so.
        &#34;&#34;&#34;
    return utils.dedent(prompt)</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyWordProcessor.actions_definitions_prompt"><code class="name flex">
<span>def <span class="ident">actions_definitions_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_definitions_prompt(self) -&gt; str:
    prompt = \
        &#34;&#34;&#34;
        - WRITE_DOCUMENT: you can create a new document. The content of the document has many fields, and you should use a JSON format to specify them. Here are the possible fields:
            * title: The title of the document. Mandatory.
            * content: The actual content of the document. You **must** use Markdown to format this content. Mandatory.
            * author: The author of the document. You should put your own name. Optional.
        &#34;&#34;&#34;
    return utils.dedent(prompt)</code></pre>
</details>
</dd>
<dt id="tinytroupe.tools.TinyWordProcessor.write_document"><code class="name flex">
<span>def <span class="ident">write_document</span></span>(<span>self, title, content, author=None)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def write_document(self, title, content, author=None):
    logger.debug(f&#34;Writing document with title {title} and content: {content}&#34;)

    if self.enricher is not None:
        requirements =\
        &#34;&#34;&#34;
        Turn any draft or outline into an actual and long document, with many, many details. Include tables, lists, and other elements.
        The result **MUST** be at least 5 times larger than the original content in terms of characters - do whatever it takes to make it this long and detailed.
        &#34;&#34;&#34;
            
        content = self.enricher.enrich_content(requirements=requirements, 
                                                content=content, 
                                                content_type=&#34;Document&#34;, 
                                                context_info=None,
                                                context_cache=None, verbose=False)    
        
    if self.exporter is not None:
        self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= content, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;md&#34;)
        self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= content, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;docx&#34;)

        json_doc = {&#34;title&#34;: title, &#34;content&#34;: content, &#34;author&#34;: author}
        self.exporter.export(artifact_name=f&#34;{title}.{author}&#34;, artifact_data= json_doc, content_type=&#34;Document&#34;, content_format=&#34;md&#34;, target_format=&#34;json&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.tools.TinyTool" href="#tinytroupe.tools.TinyTool">TinyTool</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.tools.TinyTool.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.tools.TinyTool.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="tinytroupe" href="index.html">tinytroupe</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="tinytroupe.tools.TinyCalendar" href="#tinytroupe.tools.TinyCalendar">TinyCalendar</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.tools.TinyCalendar.actions_constraints_prompt" href="#tinytroupe.tools.TinyCalendar.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.tools.TinyCalendar.actions_definitions_prompt" href="#tinytroupe.tools.TinyCalendar.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.tools.TinyCalendar.add_event" href="#tinytroupe.tools.TinyCalendar.add_event">add_event</a></code></li>
<li><code><a title="tinytroupe.tools.TinyCalendar.find_events" href="#tinytroupe.tools.TinyCalendar.find_events">find_events</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.tools.TinyTool" href="#tinytroupe.tools.TinyTool">TinyTool</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.tools.TinyTool.actions_constraints_prompt" href="#tinytroupe.tools.TinyTool.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.tools.TinyTool.actions_definitions_prompt" href="#tinytroupe.tools.TinyTool.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.tools.TinyTool.process_action" href="#tinytroupe.tools.TinyTool.process_action">process_action</a></code></li>
<li><code><a title="tinytroupe.tools.TinyTool.set_owner" href="#tinytroupe.tools.TinyTool.set_owner">set_owner</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.tools.TinyWordProcessor" href="#tinytroupe.tools.TinyWordProcessor">TinyWordProcessor</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.tools.TinyWordProcessor.actions_constraints_prompt" href="#tinytroupe.tools.TinyWordProcessor.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.tools.TinyWordProcessor.actions_definitions_prompt" href="#tinytroupe.tools.TinyWordProcessor.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.tools.TinyWordProcessor.write_document" href="#tinytroupe.tools.TinyWordProcessor.write_document">write_document</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>