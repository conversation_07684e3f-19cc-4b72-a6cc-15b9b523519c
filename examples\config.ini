[OpenAI]
#
# OpenAI or Azure OpenAI Service
#

# Default options: openai, azure
API_TYPE=openai

# Check Azure's documentation for updates here:
# https://learn.microsoft.com/en-us/azure/ai-services/openai/chatgpt-quickstart?tabs=command-line&pivots=programming-language-python
AZURE_API_VERSION=2024-08-01-preview

#
# Model parameters
#

OPENAI_BASE_URL=http://armbian.local:8082/v1
OPENAI_API_KEY=sk-XAFLlw5V6fOGpk1YYBOXJbdQ2CrvjBDd0pmVCU3tajK6UytK

MODEL=Qwen/Qwen3-8B
MAX_TOKENS=4000
TEMPERATURE=1.2
FREQ_PENALTY=0.0
PRESENCE_PENALTY=0.0
TIMEOUT=60
MAX_ATTEMPTS=5
WAITING_TIME=2
EXPONENTIAL_BACKOFF_FACTOR=5

EMBEDDING_MODEL=BAAI/bge-m3
AZURE_EMBEDDING_MODEL_API_VERSION=2023-05-15

CACHE_API_CALLS=False
CACHE_FILE_NAME=openai_api_cache.pickle

MAX_CONTENT_DISPLAY_LENGTH=1024

[Simulation]
RAI_HARMFUL_CONTENT_PREVENTION=True
RAI_COPYRIGHT_INFRINGEMENT_PREVENTION=True


[Logging]
LOGLEVEL=ERROR
# ERROR
# WARNING
# INFO
# DEBUG
