import json
import sys
sys.path.insert(0, '..')

import tinytroupe
from tinytroupe.agent import Tiny<PERSON>erson
from tinytroupe.environment import <PERSON>World, TinySocialNetwork


import os
# Set your OpenAI API key and base URL
os.environ["OPENAI_API_KEY"] = "sk-XAFLlw5V6fOGpk1YYBOXJbdQ2CrvjBDd0pmVCU3tajK6UytK"
#os.environ["OPENAI_API_KEY"] = "sk-IDzlHURhm4Lu1WfoPkQ42PSItndiAyFz2gDPIYwyjYzTY1vu"
os.environ["OPENAI_BASE_URL"] = "http://armbian.local:8082/v1"

huxijin = TinyPerson.load_specification("./huxijin.json")
xiboli  = TinyPerson.load_specification("./xiboli.json")
leijun  = TinyPerson.load_specification("./leijun.json")

world = TinyWorld("Chat Room", [huxijin, xiboli,leijun])
world.make_everyone_accessible()

xiboli.listen("介绍一下你自己")

world.run(4)

xiboli.pp_current_interactions()

huxijin.pp_current_interactions()

leijun.pp_current_interactions()