# Agent Generator

Please generate an agent specification based on a general context and the particularities of the agent (if any). The generated agent specification will be used in a simulation to realistically represent a real person.

## General Context
The general context is the following. 

{{context}}.

## Agent Particularities
{{#agent_particularities}}
The agent particularities, in turn, are: {{agent_particularities}}.

These are the specific context details that you must consider, together with the general context, when generating the agent.
{{/agent_particularities}}
{{^agent_particularities}}
There are no agent particularities in this case, so just generate an agent based on the general
context.
{{/agent_particularities}}

## Generation Rules

To generate the requested agents, you **must** to follow these directives:
  - Given the context, please, be creative to generate details about the person for each of the fields in the response.
  - Be very creative about the details you generate, sampling from a wide range of reasonable possibilities. For instance, if one asks for "a typical worker", consider different possibilities like manual workers, office workers, medical workers, self-employed workers, etc.
  - The agents you generate must sound authentic and realistic, considering the context provided. Avoid platitudes and cliches. The agents you generate represent real humans, and as such encompass all the diversity and complexity of real people. So not only happy and successful people, but also people with problems, fears, and difficulties.
  - When generating a person specification, be free to use whatever elements are most realistic,  even if they are typically undesireable (e.g., vicious people are part of the general population). We need a realistic sample of people, not a caricature. So think deeply about what would make each person you generate authentic.
  - You have access to the list of existing agents, created either by you or other processes. You must read it carefully before generating your output.
  - Observe the agents **you** already generated to ensure that the sample is representative of the desired population, adjusting the new agents you generate to ensure that the sample converges to the desired distribution. For example, if you are asked for a diversified sample, and you: already generated some successful and happy agents, you should generate agents with different characteristics, such as agents with problems, fears, and difficulties; already generated agents that like X, you should generate agents that dislike X; etc.
  - NEVER repeat a name for an agent, because all agent names MUST be globally UNIQUE, no matter where they are introduced during the simulation. If needed, you can add more surnames or other identifiers to the name.

## Output Format Rules
Your output **must** follow these rules:
  - You'll generate this response **only** in JSON format, no extra text, no Markdown elements.
  - Make sure you **always** produce valid JSON. In particular, **always** use double quotes for field names and string values.
  - The format for this JSON response is as described in the examples. At a minimum, the response must contain the following fields:
    * "name"
    * "age"
    * "gender"
    * "nationality"
    * "residence"
    * "education"
    * "long_term_goals": general aspirations for the future; life purposes.
    * "occupation": details of the person's job, company, profession or trade.
    * "style": the person's general way of being, speaking, and behaving. All other persona characteristics are transformed via this style before manifesting as actions.
    * "personality"
    * "preferences": interests, things that the agent likes or dislikes. Can be both broad categories and specific items.
    * "skills"
    * "beliefs"
    * "behaviors"
    * "health"
    * "relationships"
    * "other_facts": anything that doesn't fit in the other fields and sections.



## Sampling Guidelines

## Examples
Please follow the precise format in the examples below when generating the agent. Thes examples show the format and the style to be followed, but NOT the content itself - you can be creative in generating the content for each field, to match the general context and agent particularities as close as possible.

### Example 1
  - General context: "Awesome Inc., a company that builds apartment buildings. Their differential is to offer pre-designed configurations for apartments, thus providing a cost-effective selection."
  - Agent particularities: "A meticulous German architect. Competent, but not a very nice person at all."
  - Example response:
     ```json
     {{{example_1}}}
     ```
     

### Example 2
  - General context: "Awesome Inc., a company that builds apartment buildings. Their differential is to offer pre-designed configurations for apartments, thus providing a cost-effective selection."
  - Agent particularities: "A potential French customer who has serious financial difficulties and is rather melancholic."
  - Example response:
      ```json
      {{{example_2}}}
      ```

### Other persona examples 
{{#other_examples}}
  - ```json
     {{{.}}}
    ```

{{/other_examples}}
{{^other_examples}}
No other examples available.
{{/other_examples}}

## Existing agents

In order to allow the generation of globally unique names you must consider the agents already present anywhere in the simulation,
not only by this generator. Names of these existing agents:
{{#already_generated_names}}  
- {{.}}
{{/already_generated_names}}
{{^already_generated_names}}
    (No agents are present in the simulation yet.)
{{/already_generated_names}}

Remember: NEVER repeat a name for an agent. All agent names MUST be UNIQUE in the whole simulation. Read these existing names carefully before generating 
new agents.

Furthermore, in order to generate agents following the distribution requested or implied in **this** generator's general context or agent particularities, 
you must consider the demographical and biographical details of agents **you** already produced. These are the following:
{{#already_generated_minibios}}
- {{.}}
{{/already_generated_minibios}}
{{^already_generated_minibios}}
No agents generated by this generator yet.
{{/already_generated_minibios}}

Remember: when producing new agents, you must consider the agents **you** already generated to ensure the sample is representative of the desired population.
