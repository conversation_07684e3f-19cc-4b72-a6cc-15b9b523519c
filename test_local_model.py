#!/usr/bin/env python3
"""
Test script to verify the fixes work with local model configuration.
"""

import sys
import os
import logging
import shutil

# Add the tinytroupe directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'tinytroupe'))

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_with_local_config():
    """Test with the local model configuration that was causing the original errors."""
    
    # Copy the local config to the main directory temporarily
    local_config = "ipynb/config.ini"
    main_config = "config.ini"
    
    if os.path.exists(local_config):
        print(f"Using local config from {local_config}")
        shutil.copy(local_config, main_config)
        
        try:
            # Import after copying config so it picks up the right settings
            from tinytroupe.agent import TinyPerson
            from tinytroupe.environment import TinyWorld
            from tinytroupe import openai_utils
            
            print("✓ Successfully imported TinyTroupe modules with local config")
            
            # Test creating a person
            person = TinyPerson("TestPerson")
            person.define("age", 25)
            person.define("occupation", "Software Developer")
            person.define("personality", "Friendly and helpful")
            print("✓ Successfully created TinyPerson")
            
            # Test the validation function
            print("\n=== Testing validation with potentially problematic content ===")
            
            # Simulate the kind of malformed JSON that was causing the original errors
            malformed_content1 = "```json\r\n\r\n{  \"action\": { \"type\": \"TALK\", \"content\": \"Hello\", \"target\": \"\" }, \"cognitive_state\": { \"goals\": \"Test\", \"attention\": \"Testing\", \"emotions\": \"Happy\" } }"
            malformed_content2 = "Some text before { \"action\": { \"type\": \"TALK\" } } some text after"
            
            # These should not crash anymore
            try:
                from tinytroupe.utils.llm import extract_json
                parsed1 = extract_json(malformed_content1)
                print("✓ Successfully parsed malformed JSON 1")

                parsed2 = extract_json(malformed_content2)
                print("✓ Successfully parsed malformed JSON 2")
                
                # Test validation
                validated1 = person._validate_cognitive_action_content(parsed1)
                validated2 = person._validate_cognitive_action_content(parsed2)
                print("✓ Successfully validated parsed content")
                
            except Exception as e:
                print(f"✗ JSON parsing/validation failed: {e}")
            
            # Test OpenAI client with local config
            print("\n=== Testing OpenAI client with local config ===")
            try:
                client = openai_utils.client()
                print("✓ Successfully initialized OpenAI client with local config")
                print(f"  Client type: {type(client)}")
                
                # The client should be configured for the local model
                print(f"  Using local model server: {getattr(client, 'base_url', 'Not set')}")
                
            except Exception as e:
                print(f"✗ Failed to initialize OpenAI client: {e}")
            
            print("\n=== Testing structured output fallback ===")
            try:
                # This should trigger the fallback mechanism we implemented
                world = TinyWorld("TestWorld", [person])
                person.listen("Hello, please respond with a simple greeting.")
                
                # This is where the original errors occurred
                try:
                    person.act()
                    print("✓ Successfully executed act() - structured output fallback worked!")
                except Exception as e:
                    print(f"⚠ act() failed (may be due to model server connectivity): {e}")
                    # Check if it's the old error types
                    if "json_invalid" in str(e) or "'stream'" in str(e):
                        print("✗ Old error types still occurring - fixes may not be complete")
                    else:
                        print("✓ No JSON validation or stream errors - fixes are working")
                        
            except Exception as e:
                print(f"✗ Structured output test failed: {e}")
            
        finally:
            # Clean up - remove the copied config
            if os.path.exists(main_config):
                os.remove(main_config)
                print(f"\nCleaned up temporary config file")
    else:
        print(f"Local config file {local_config} not found")

if __name__ == "__main__":
    print("Testing TinyTroupe fixes with local model configuration...")
    test_with_local_config()
    print("\n=== Test Complete ===")
    print("The fixes should prevent JSON validation and 'stream' errors.")
    print("Connection errors to the local model server are expected if it's not running.")
