<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>tinytroupe API documentation</title>
<meta name="description" content="" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Package <code>tinytroupe</code></h1>
</header>
<section id="section-intro">
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">import os
import logging
import configparser
import rich # for rich console output
import rich.jupyter

# add current path to sys.path
import sys
sys.path.append(&#39;.&#39;)
from tinytroupe import utils # now we can import our utils

# AI disclaimers
print(\
&#34;&#34;&#34;
!!!!
DISCLAIMER: TinyTroupe relies on Artificial Intelligence (AI) models to generate content. 
The AI models are not perfect and may produce inappropriate or inacurate results. 
For any serious or consequential use, please review the generated content before using it.
!!!!
&#34;&#34;&#34;)

config = utils.read_config_file()
utils.pretty_print_config(config)
utils.start_logger(config)

# fix an issue in the rich library: we don&#39;t want margins in Jupyter!
rich.jupyter.JUPYTER_HTML_FORMAT = \
    utils.inject_html_css_style_prefix(rich.jupyter.JUPYTER_HTML_FORMAT, &#34;margin:0px;&#34;)</code></pre>
</details>
</section>
<section>
<h2 class="section-title" id="header-submodules">Sub-modules</h2>
<dl>
<dt><code class="name"><a title="tinytroupe.agent" href="agent.html">tinytroupe.agent</a></code></dt>
<dd>
<div class="desc"><p>This module provides the main classes and functions for TinyTroupe's
agents …</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.control" href="control.html">tinytroupe.control</a></code></dt>
<dd>
<div class="desc"><p>Simulation controlling mechanisms.</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.enrichment" href="enrichment.html">tinytroupe.enrichment</a></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt><code class="name"><a title="tinytroupe.environment" href="environment.html">tinytroupe.environment</a></code></dt>
<dd>
<div class="desc"><p>Environments provide a structured way to define the world in which the
agents interact with each other as well as external entities (e.g., search …</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.examples" href="examples.html">tinytroupe.examples</a></code></dt>
<dd>
<div class="desc"><p>Some examples of how to use the tinytroupe library. These can be used directly or slightly modified to create your own '
agents.</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.experimentation" href="experimentation.html">tinytroupe.experimentation</a></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt><code class="name"><a title="tinytroupe.extraction" href="extraction.html">tinytroupe.extraction</a></code></dt>
<dd>
<div class="desc"><p>Simulations produce a lot of data, and it is often useful to extract these data in a structured way. For instance, you might wish to:
- Extract the …</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.factory" href="factory.html">tinytroupe.factory</a></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt><code class="name"><a title="tinytroupe.openai_utils" href="openai_utils.html">tinytroupe.openai_utils</a></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt><code class="name"><a title="tinytroupe.profiling" href="profiling.html">tinytroupe.profiling</a></code></dt>
<dd>
<div class="desc"><p>Provides mechanisms for creating understanding the characteristics of agent populations, such as
their age distribution, typical interests, and so on …</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.story" href="story.html">tinytroupe.story</a></code></dt>
<dd>
<div class="desc"><p>Every simulation tells a story. This module provides helper mechanisms to help with crafting appropriate stories in TinyTroupe.</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.tools" href="tools.html">tinytroupe.tools</a></code></dt>
<dd>
<div class="desc"><p>Tools allow agents to accomplish specialized tasks.</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.utils" href="utils.html">tinytroupe.utils</a></code></dt>
<dd>
<div class="desc"><p>General utilities and convenience functions.</p></div>
</dd>
<dt><code class="name"><a title="tinytroupe.validation" href="validation.html">tinytroupe.validation</a></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3><a href="#header-submodules">Sub-modules</a></h3>
<ul>
<li><code><a title="tinytroupe.agent" href="agent.html">tinytroupe.agent</a></code></li>
<li><code><a title="tinytroupe.control" href="control.html">tinytroupe.control</a></code></li>
<li><code><a title="tinytroupe.enrichment" href="enrichment.html">tinytroupe.enrichment</a></code></li>
<li><code><a title="tinytroupe.environment" href="environment.html">tinytroupe.environment</a></code></li>
<li><code><a title="tinytroupe.examples" href="examples.html">tinytroupe.examples</a></code></li>
<li><code><a title="tinytroupe.experimentation" href="experimentation.html">tinytroupe.experimentation</a></code></li>
<li><code><a title="tinytroupe.extraction" href="extraction.html">tinytroupe.extraction</a></code></li>
<li><code><a title="tinytroupe.factory" href="factory.html">tinytroupe.factory</a></code></li>
<li><code><a title="tinytroupe.openai_utils" href="openai_utils.html">tinytroupe.openai_utils</a></code></li>
<li><code><a title="tinytroupe.profiling" href="profiling.html">tinytroupe.profiling</a></code></li>
<li><code><a title="tinytroupe.story" href="story.html">tinytroupe.story</a></code></li>
<li><code><a title="tinytroupe.tools" href="tools.html">tinytroupe.tools</a></code></li>
<li><code><a title="tinytroupe.utils" href="utils.html">tinytroupe.utils</a></code></li>
<li><code><a title="tinytroupe.validation" href="validation.html">tinytroupe.validation</a></code></li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>