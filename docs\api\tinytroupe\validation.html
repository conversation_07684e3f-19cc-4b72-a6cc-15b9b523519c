<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>tinytroupe.validation API documentation</title>
<meta name="description" content="" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>tinytroupe.validation</code></h1>
</header>
<section id="section-intro">
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">import os
import json
import chevron
import logging

from tinytroupe import openai_utils
from tinytroupe.agent import TinyPerson
from tinytroupe import config
import tinytroupe.utils as utils


default_max_content_display_length = config[&#34;OpenAI&#34;].getint(&#34;MAX_CONTENT_DISPLAY_LENGTH&#34;, 1024)


class TinyPersonValidator:

    @staticmethod
    def validate_person(person, expectations=None, include_agent_spec=True, max_content_length=default_max_content_display_length):
        &#34;&#34;&#34;
        Validate a TinyPerson instance using OpenAI&#39;s LLM.

        This method sends a series of questions to the TinyPerson instance to validate its responses using OpenAI&#39;s LLM.
        The method returns a float value representing the confidence score of the validation process.
        If the validation process fails, the method returns None.

        Args:
            person (TinyPerson): The TinyPerson instance to be validated.
            expectations (str, optional): The expectations to be used in the validation process. Defaults to None.
            include_agent_spec (bool, optional): Whether to include the agent specification in the prompt. Defaults to True.
            max_content_length (int, optional): The maximum length of the content to be displayed when rendering the conversation.

        Returns:
            float: The confidence score of the validation process (0.0 to 1.0), or None if the validation process fails.
            str: The justification for the validation score, or None if the validation process fails.
        &#34;&#34;&#34;
        # Initiating the current messages
        current_messages = []
        
        # Generating the prompt to check the person
        check_person_prompt_template_path = os.path.join(os.path.dirname(__file__), &#39;prompts/check_person.mustache&#39;)
        with open(check_person_prompt_template_path, &#39;r&#39;) as f:
            check_agent_prompt_template = f.read()
        
        system_prompt = chevron.render(check_agent_prompt_template, {&#34;expectations&#34;: expectations})

        # use dedent
        import textwrap
        user_prompt = textwrap.dedent(\
        &#34;&#34;&#34;
        Now, based on the following characteristics of the person being interviewed, and following the rules given previously, 
        create your questions and interview the person. Good luck!

        &#34;&#34;&#34;)

        if include_agent_spec:
            user_prompt += f&#34;\n\n{person.generate_agent_specification()}&#34;
        else:
            user_prompt += f&#34;\n\nMini-biography of the person being interviewed: {person.minibio()}&#34;


        logger = logging.getLogger(&#34;tinytroupe&#34;)

        logger.info(f&#34;Starting validation of the person: {person.name}&#34;)

        # Sending the initial messages to the LLM
        current_messages.append({&#34;role&#34;: &#34;system&#34;, &#34;content&#34;: system_prompt})
        current_messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: user_prompt})

        message = openai_utils.client().send_message(current_messages)

        # What string to look for to terminate the conversation
        termination_mark = &#34;```json&#34;

        while message is not None and not (termination_mark in message[&#34;content&#34;]):
            # Appending the questions to the current messages
            questions = message[&#34;content&#34;]
            current_messages.append({&#34;role&#34;: message[&#34;role&#34;], &#34;content&#34;: questions})
            logger.info(f&#34;Question validation:\n{questions}&#34;)

            # Asking the questions to the person
            person.listen_and_act(questions, max_content_length=max_content_length)
            responses = person.pop_actions_and_get_contents_for(&#34;TALK&#34;, False)
            logger.info(f&#34;Person reply:\n{responses}&#34;)

            # Appending the responses to the current conversation and checking the next message
            current_messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: responses})
            message = openai_utils.client().send_message(current_messages)

        if message is not None:
            json_content = utils.extract_json(message[&#39;content&#39;])
            # read score and justification
            score = float(json_content[&#34;score&#34;])
            justification = json_content[&#34;justification&#34;]
            logger.info(f&#34;Validation score: {score:.2f}; Justification: {justification}&#34;)
            
            return score, justification
        
        else:
            return None, None</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="tinytroupe.validation.TinyPersonValidator"><code class="flex name class">
<span>class <span class="ident">TinyPersonValidator</span></span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyPersonValidator:

    @staticmethod
    def validate_person(person, expectations=None, include_agent_spec=True, max_content_length=default_max_content_display_length):
        &#34;&#34;&#34;
        Validate a TinyPerson instance using OpenAI&#39;s LLM.

        This method sends a series of questions to the TinyPerson instance to validate its responses using OpenAI&#39;s LLM.
        The method returns a float value representing the confidence score of the validation process.
        If the validation process fails, the method returns None.

        Args:
            person (TinyPerson): The TinyPerson instance to be validated.
            expectations (str, optional): The expectations to be used in the validation process. Defaults to None.
            include_agent_spec (bool, optional): Whether to include the agent specification in the prompt. Defaults to True.
            max_content_length (int, optional): The maximum length of the content to be displayed when rendering the conversation.

        Returns:
            float: The confidence score of the validation process (0.0 to 1.0), or None if the validation process fails.
            str: The justification for the validation score, or None if the validation process fails.
        &#34;&#34;&#34;
        # Initiating the current messages
        current_messages = []
        
        # Generating the prompt to check the person
        check_person_prompt_template_path = os.path.join(os.path.dirname(__file__), &#39;prompts/check_person.mustache&#39;)
        with open(check_person_prompt_template_path, &#39;r&#39;) as f:
            check_agent_prompt_template = f.read()
        
        system_prompt = chevron.render(check_agent_prompt_template, {&#34;expectations&#34;: expectations})

        # use dedent
        import textwrap
        user_prompt = textwrap.dedent(\
        &#34;&#34;&#34;
        Now, based on the following characteristics of the person being interviewed, and following the rules given previously, 
        create your questions and interview the person. Good luck!

        &#34;&#34;&#34;)

        if include_agent_spec:
            user_prompt += f&#34;\n\n{person.generate_agent_specification()}&#34;
        else:
            user_prompt += f&#34;\n\nMini-biography of the person being interviewed: {person.minibio()}&#34;


        logger = logging.getLogger(&#34;tinytroupe&#34;)

        logger.info(f&#34;Starting validation of the person: {person.name}&#34;)

        # Sending the initial messages to the LLM
        current_messages.append({&#34;role&#34;: &#34;system&#34;, &#34;content&#34;: system_prompt})
        current_messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: user_prompt})

        message = openai_utils.client().send_message(current_messages)

        # What string to look for to terminate the conversation
        termination_mark = &#34;```json&#34;

        while message is not None and not (termination_mark in message[&#34;content&#34;]):
            # Appending the questions to the current messages
            questions = message[&#34;content&#34;]
            current_messages.append({&#34;role&#34;: message[&#34;role&#34;], &#34;content&#34;: questions})
            logger.info(f&#34;Question validation:\n{questions}&#34;)

            # Asking the questions to the person
            person.listen_and_act(questions, max_content_length=max_content_length)
            responses = person.pop_actions_and_get_contents_for(&#34;TALK&#34;, False)
            logger.info(f&#34;Person reply:\n{responses}&#34;)

            # Appending the responses to the current conversation and checking the next message
            current_messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: responses})
            message = openai_utils.client().send_message(current_messages)

        if message is not None:
            json_content = utils.extract_json(message[&#39;content&#39;])
            # read score and justification
            score = float(json_content[&#34;score&#34;])
            justification = json_content[&#34;justification&#34;]
            logger.info(f&#34;Validation score: {score:.2f}; Justification: {justification}&#34;)
            
            return score, justification
        
        else:
            return None, None</code></pre>
</details>
<h3>Static methods</h3>
<dl>
<dt id="tinytroupe.validation.TinyPersonValidator.validate_person"><code class="name flex">
<span>def <span class="ident">validate_person</span></span>(<span>person, expectations=None, include_agent_spec=True, max_content_length=1024)</span>
</code></dt>
<dd>
<div class="desc"><p>Validate a TinyPerson instance using OpenAI's LLM.</p>
<p>This method sends a series of questions to the TinyPerson instance to validate its responses using OpenAI's LLM.
The method returns a float value representing the confidence score of the validation process.
If the validation process fails, the method returns None.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>person</code></strong> :&ensp;<code>TinyPerson</code></dt>
<dd>The TinyPerson instance to be validated.</dd>
<dt><strong><code>expectations</code></strong> :&ensp;<code>str</code>, optional</dt>
<dd>The expectations to be used in the validation process. Defaults to None.</dd>
<dt><strong><code>include_agent_spec</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to include the agent specification in the prompt. Defaults to True.</dd>
<dt><strong><code>max_content_length</code></strong> :&ensp;<code>int</code>, optional</dt>
<dd>The maximum length of the content to be displayed when rendering the conversation.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>float</code></dt>
<dd>The confidence score of the validation process (0.0 to 1.0), or None if the validation process fails.</dd>
<dt><code>str</code></dt>
<dd>The justification for the validation score, or None if the validation process fails.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@staticmethod
def validate_person(person, expectations=None, include_agent_spec=True, max_content_length=default_max_content_display_length):
    &#34;&#34;&#34;
    Validate a TinyPerson instance using OpenAI&#39;s LLM.

    This method sends a series of questions to the TinyPerson instance to validate its responses using OpenAI&#39;s LLM.
    The method returns a float value representing the confidence score of the validation process.
    If the validation process fails, the method returns None.

    Args:
        person (TinyPerson): The TinyPerson instance to be validated.
        expectations (str, optional): The expectations to be used in the validation process. Defaults to None.
        include_agent_spec (bool, optional): Whether to include the agent specification in the prompt. Defaults to True.
        max_content_length (int, optional): The maximum length of the content to be displayed when rendering the conversation.

    Returns:
        float: The confidence score of the validation process (0.0 to 1.0), or None if the validation process fails.
        str: The justification for the validation score, or None if the validation process fails.
    &#34;&#34;&#34;
    # Initiating the current messages
    current_messages = []
    
    # Generating the prompt to check the person
    check_person_prompt_template_path = os.path.join(os.path.dirname(__file__), &#39;prompts/check_person.mustache&#39;)
    with open(check_person_prompt_template_path, &#39;r&#39;) as f:
        check_agent_prompt_template = f.read()
    
    system_prompt = chevron.render(check_agent_prompt_template, {&#34;expectations&#34;: expectations})

    # use dedent
    import textwrap
    user_prompt = textwrap.dedent(\
    &#34;&#34;&#34;
    Now, based on the following characteristics of the person being interviewed, and following the rules given previously, 
    create your questions and interview the person. Good luck!

    &#34;&#34;&#34;)

    if include_agent_spec:
        user_prompt += f&#34;\n\n{person.generate_agent_specification()}&#34;
    else:
        user_prompt += f&#34;\n\nMini-biography of the person being interviewed: {person.minibio()}&#34;


    logger = logging.getLogger(&#34;tinytroupe&#34;)

    logger.info(f&#34;Starting validation of the person: {person.name}&#34;)

    # Sending the initial messages to the LLM
    current_messages.append({&#34;role&#34;: &#34;system&#34;, &#34;content&#34;: system_prompt})
    current_messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: user_prompt})

    message = openai_utils.client().send_message(current_messages)

    # What string to look for to terminate the conversation
    termination_mark = &#34;```json&#34;

    while message is not None and not (termination_mark in message[&#34;content&#34;]):
        # Appending the questions to the current messages
        questions = message[&#34;content&#34;]
        current_messages.append({&#34;role&#34;: message[&#34;role&#34;], &#34;content&#34;: questions})
        logger.info(f&#34;Question validation:\n{questions}&#34;)

        # Asking the questions to the person
        person.listen_and_act(questions, max_content_length=max_content_length)
        responses = person.pop_actions_and_get_contents_for(&#34;TALK&#34;, False)
        logger.info(f&#34;Person reply:\n{responses}&#34;)

        # Appending the responses to the current conversation and checking the next message
        current_messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: responses})
        message = openai_utils.client().send_message(current_messages)

    if message is not None:
        json_content = utils.extract_json(message[&#39;content&#39;])
        # read score and justification
        score = float(json_content[&#34;score&#34;])
        justification = json_content[&#34;justification&#34;]
        logger.info(f&#34;Validation score: {score:.2f}; Justification: {justification}&#34;)
        
        return score, justification
    
    else:
        return None, None</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="tinytroupe" href="index.html">tinytroupe</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="tinytroupe.validation.TinyPersonValidator" href="#tinytroupe.validation.TinyPersonValidator">TinyPersonValidator</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.validation.TinyPersonValidator.validate_person" href="#tinytroupe.validation.TinyPersonValidator.validate_person">validate_person</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>