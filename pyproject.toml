[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["tinytroupe"]
include-package-data = true

[project]
name = "tinytroupe"
version = "0.4.0"
authors = [
  { name="Paulo Salem", email="<EMAIL>" }
]
description = "LLM-based people simulation for design, validation and insight generation in business."
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

dependencies = [
    "pandas", 
    "pytest", "pytest-cov",
    "openai >= 1.40", 
    "tiktoken",
    "msal",
    "rich", "requests", "chevron",
    "llama-index", "llama-index-embeddings-huggingface", "llama-index-readers-web", "llama-index-embeddings-azure-openai",
    "pypandoc", "docx", "markdown",
    "jupyter",
    "matplotlib",
    "pydantic",
    "pypandoc" 
]

[project.urls]
"Homepage" = "https://github.com/microsoft/tinytroupe"

[tool.pytest.ini_options]
pythonpath = [
  "."
]
addopts = "--cov=. --cov-report=html --cov-report=xml"