<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>tinytroupe.story API documentation</title>
<meta name="description" content="Every simulation tells a story. This module provides helper mechanisms to help with crafting appropriate stories in TinyTroupe." />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>tinytroupe.story</code></h1>
</header>
<section id="section-intro">
<p>Every simulation tells a story. This module provides helper mechanisms to help with crafting appropriate stories in TinyTroupe.</p>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">&#34;&#34;&#34;
Every simulation tells a story. This module provides helper mechanisms to help with crafting appropriate stories in TinyTroupe.
&#34;&#34;&#34;

from typing import List
from tinytroupe.agent import TinyPerson
from tinytroupe.environment import TinyWorld
import tinytroupe.utils as utils
from tinytroupe import openai_utils

class TinyStory:

    def __init__(self, environment:TinyWorld=None, agent:TinyPerson=None, purpose:str=&#34;Be a realistic simulation.&#34;, context:str=&#34;&#34;,
                 first_n=10, last_n=20, include_omission_info:bool=True) -&gt; None:
        &#34;&#34;&#34;
        Initialize the story. The story can be about an environment or an agent. It also has a purpose, which
        is used to guide the story generation. Stories are aware that they are related to simulations, so one can
        specify simulation-related purposes.

        Args:
            environment (TinyWorld, optional): The environment in which the story takes place. Defaults to None.
            agent (TinyPerson, optional): The agent in the story. Defaults to None.
            purpose (str, optional): The purpose of the story. Defaults to &#34;Be a realistic simulation.&#34;.
            context (str, optional): The current story context. Defaults to &#34;&#34;. The actual story will be appended to this context.
            first_n (int, optional): The number of first interactions to include in the story. Defaults to 10.
            last_n (int, optional): The number of last interactions to include in the story. Defaults to 20.
            include_omission_info (bool, optional): Whether to include information about omitted interactions. Defaults to True.
        &#34;&#34;&#34;
        
        # exactly one of these must be provided
        if environment and agent:
            raise Exception(&#34;Either &#39;environment&#39; or &#39;agent&#39; should be provided, not both&#34;)
        if not (environment or agent):
            raise Exception(&#34;At least one of the parameters should be provided&#34;)
        
        self.environment = environment
        self.agent = agent

        self.purpose = purpose

        self.current_story = context

        self.first_n = first_n
        self.last_n = last_n
        self.include_omission_info = include_omission_info
    
    def start_story(self, requirements=&#34;Start some interesting story about the agents.&#34;, number_of_words:int=100, include_plot_twist:bool=False) -&gt; str:
        &#34;&#34;&#34;
        Start a new story.
        &#34;&#34;&#34;
        
        rendering_configs = {
                             &#34;purpose&#34;: self.purpose,
                             &#34;requirements&#34;: requirements,
                             &#34;current_simulation_trace&#34;: self._current_story(),
                             &#34;number_of_words&#34;: number_of_words,
                             &#34;include_plot_twist&#34;: include_plot_twist
                            }

        messages = utils.compose_initial_LLM_messages_with_templates(&#34;story.start.system.mustache&#34;, &#34;story.start.user.mustache&#34;, rendering_configs)
        next_message = openai_utils.client().send_message(messages, temperature=1.5)

        start = next_message[&#34;content&#34;]

        self.current_story += utils.dedent(\
            f&#34;&#34;&#34;

            ## The story begins

            {start}

            &#34;&#34;&#34;
            )

        return start
    
    def continue_story(self, requirements=&#34;Continue the story in an interesting way.&#34;, number_of_words:int=100, include_plot_twist:bool=False) -&gt; str:
        &#34;&#34;&#34;
        Propose a continuation of the story.
        &#34;&#34;&#34;
        
        rendering_configs = {
                             &#34;purpose&#34;: self.purpose,
                             &#34;requirements&#34;: requirements,
                             &#34;current_simulation_trace&#34;: self._current_story(),
                             &#34;number_of_words&#34;: number_of_words,
                             &#34;include_plot_twist&#34;: include_plot_twist
                            }

        messages = utils.compose_initial_LLM_messages_with_templates(&#34;story.continuation.system.mustache&#34;, &#34;story.continuation.user.mustache&#34;, rendering_configs)
        next_message = openai_utils.client().send_message(messages, temperature=1.5)

        continuation = next_message[&#34;content&#34;]

        self.current_story += utils.dedent(\
            f&#34;&#34;&#34;

            ## The story continues

            {continuation}

            &#34;&#34;&#34;
            )

        return continuation

    def _current_story(self) -&gt; str:
        &#34;&#34;&#34;
        Get the current story.
        &#34;&#34;&#34;
        interaction_history = &#34;&#34;
        
        if self.agent is not None:
            interaction_history += self.agent.pretty_current_interactions(first_n=self.first_n, last_n=self.last_n, include_omission_info=self.include_omission_info)
        elif self.environment is not None:
            interaction_history += self.environment.pretty_current_interactions(first_n=self.first_n, last_n=self.last_n, include_omission_info=self.include_omission_info)

        self.current_story += utils.dedent(\
            f&#34;&#34;&#34;

            ## New simulation interactions to consider

            {interaction_history}

            &#34;&#34;&#34;
            )
            
        return self.current_story
            
        </code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="tinytroupe.story.TinyStory"><code class="flex name class">
<span>class <span class="ident">TinyStory</span></span>
<span>(</span><span>environment: <a title="tinytroupe.environment.TinyWorld" href="environment.html#tinytroupe.environment.TinyWorld">TinyWorld</a> = None, agent: <a title="tinytroupe.agent.TinyPerson" href="agent.html#tinytroupe.agent.TinyPerson">TinyPerson</a> = None, purpose: str = 'Be a realistic simulation.', context: str = '', first_n=10, last_n=20, include_omission_info: bool = True)</span>
</code></dt>
<dd>
<div class="desc"><p>Initialize the story. The story can be about an environment or an agent. It also has a purpose, which
is used to guide the story generation. Stories are aware that they are related to simulations, so one can
specify simulation-related purposes.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>environment</code></strong> :&ensp;<code>TinyWorld</code>, optional</dt>
<dd>The environment in which the story takes place. Defaults to None.</dd>
<dt><strong><code>agent</code></strong> :&ensp;<code>TinyPerson</code>, optional</dt>
<dd>The agent in the story. Defaults to None.</dd>
<dt><strong><code>purpose</code></strong> :&ensp;<code>str</code>, optional</dt>
<dd>The purpose of the story. Defaults to "Be a realistic simulation.".</dd>
<dt><strong><code>context</code></strong> :&ensp;<code>str</code>, optional</dt>
<dd>The current story context. Defaults to "". The actual story will be appended to this context.</dd>
<dt><strong><code>first_n</code></strong> :&ensp;<code>int</code>, optional</dt>
<dd>The number of first interactions to include in the story. Defaults to 10.</dd>
<dt><strong><code>last_n</code></strong> :&ensp;<code>int</code>, optional</dt>
<dd>The number of last interactions to include in the story. Defaults to 20.</dd>
<dt><strong><code>include_omission_info</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to include information about omitted interactions. Defaults to True.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyStory:

    def __init__(self, environment:TinyWorld=None, agent:TinyPerson=None, purpose:str=&#34;Be a realistic simulation.&#34;, context:str=&#34;&#34;,
                 first_n=10, last_n=20, include_omission_info:bool=True) -&gt; None:
        &#34;&#34;&#34;
        Initialize the story. The story can be about an environment or an agent. It also has a purpose, which
        is used to guide the story generation. Stories are aware that they are related to simulations, so one can
        specify simulation-related purposes.

        Args:
            environment (TinyWorld, optional): The environment in which the story takes place. Defaults to None.
            agent (TinyPerson, optional): The agent in the story. Defaults to None.
            purpose (str, optional): The purpose of the story. Defaults to &#34;Be a realistic simulation.&#34;.
            context (str, optional): The current story context. Defaults to &#34;&#34;. The actual story will be appended to this context.
            first_n (int, optional): The number of first interactions to include in the story. Defaults to 10.
            last_n (int, optional): The number of last interactions to include in the story. Defaults to 20.
            include_omission_info (bool, optional): Whether to include information about omitted interactions. Defaults to True.
        &#34;&#34;&#34;
        
        # exactly one of these must be provided
        if environment and agent:
            raise Exception(&#34;Either &#39;environment&#39; or &#39;agent&#39; should be provided, not both&#34;)
        if not (environment or agent):
            raise Exception(&#34;At least one of the parameters should be provided&#34;)
        
        self.environment = environment
        self.agent = agent

        self.purpose = purpose

        self.current_story = context

        self.first_n = first_n
        self.last_n = last_n
        self.include_omission_info = include_omission_info
    
    def start_story(self, requirements=&#34;Start some interesting story about the agents.&#34;, number_of_words:int=100, include_plot_twist:bool=False) -&gt; str:
        &#34;&#34;&#34;
        Start a new story.
        &#34;&#34;&#34;
        
        rendering_configs = {
                             &#34;purpose&#34;: self.purpose,
                             &#34;requirements&#34;: requirements,
                             &#34;current_simulation_trace&#34;: self._current_story(),
                             &#34;number_of_words&#34;: number_of_words,
                             &#34;include_plot_twist&#34;: include_plot_twist
                            }

        messages = utils.compose_initial_LLM_messages_with_templates(&#34;story.start.system.mustache&#34;, &#34;story.start.user.mustache&#34;, rendering_configs)
        next_message = openai_utils.client().send_message(messages, temperature=1.5)

        start = next_message[&#34;content&#34;]

        self.current_story += utils.dedent(\
            f&#34;&#34;&#34;

            ## The story begins

            {start}

            &#34;&#34;&#34;
            )

        return start
    
    def continue_story(self, requirements=&#34;Continue the story in an interesting way.&#34;, number_of_words:int=100, include_plot_twist:bool=False) -&gt; str:
        &#34;&#34;&#34;
        Propose a continuation of the story.
        &#34;&#34;&#34;
        
        rendering_configs = {
                             &#34;purpose&#34;: self.purpose,
                             &#34;requirements&#34;: requirements,
                             &#34;current_simulation_trace&#34;: self._current_story(),
                             &#34;number_of_words&#34;: number_of_words,
                             &#34;include_plot_twist&#34;: include_plot_twist
                            }

        messages = utils.compose_initial_LLM_messages_with_templates(&#34;story.continuation.system.mustache&#34;, &#34;story.continuation.user.mustache&#34;, rendering_configs)
        next_message = openai_utils.client().send_message(messages, temperature=1.5)

        continuation = next_message[&#34;content&#34;]

        self.current_story += utils.dedent(\
            f&#34;&#34;&#34;

            ## The story continues

            {continuation}

            &#34;&#34;&#34;
            )

        return continuation

    def _current_story(self) -&gt; str:
        &#34;&#34;&#34;
        Get the current story.
        &#34;&#34;&#34;
        interaction_history = &#34;&#34;
        
        if self.agent is not None:
            interaction_history += self.agent.pretty_current_interactions(first_n=self.first_n, last_n=self.last_n, include_omission_info=self.include_omission_info)
        elif self.environment is not None:
            interaction_history += self.environment.pretty_current_interactions(first_n=self.first_n, last_n=self.last_n, include_omission_info=self.include_omission_info)

        self.current_story += utils.dedent(\
            f&#34;&#34;&#34;

            ## New simulation interactions to consider

            {interaction_history}

            &#34;&#34;&#34;
            )
            
        return self.current_story</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.story.TinyStory.continue_story"><code class="name flex">
<span>def <span class="ident">continue_story</span></span>(<span>self, requirements='Continue the story in an interesting way.', number_of_words: int = 100, include_plot_twist: bool = False) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Propose a continuation of the story.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def continue_story(self, requirements=&#34;Continue the story in an interesting way.&#34;, number_of_words:int=100, include_plot_twist:bool=False) -&gt; str:
    &#34;&#34;&#34;
    Propose a continuation of the story.
    &#34;&#34;&#34;
    
    rendering_configs = {
                         &#34;purpose&#34;: self.purpose,
                         &#34;requirements&#34;: requirements,
                         &#34;current_simulation_trace&#34;: self._current_story(),
                         &#34;number_of_words&#34;: number_of_words,
                         &#34;include_plot_twist&#34;: include_plot_twist
                        }

    messages = utils.compose_initial_LLM_messages_with_templates(&#34;story.continuation.system.mustache&#34;, &#34;story.continuation.user.mustache&#34;, rendering_configs)
    next_message = openai_utils.client().send_message(messages, temperature=1.5)

    continuation = next_message[&#34;content&#34;]

    self.current_story += utils.dedent(\
        f&#34;&#34;&#34;

        ## The story continues

        {continuation}

        &#34;&#34;&#34;
        )

    return continuation</code></pre>
</details>
</dd>
<dt id="tinytroupe.story.TinyStory.start_story"><code class="name flex">
<span>def <span class="ident">start_story</span></span>(<span>self, requirements='Start some interesting story about the agents.', number_of_words: int = 100, include_plot_twist: bool = False) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Start a new story.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def start_story(self, requirements=&#34;Start some interesting story about the agents.&#34;, number_of_words:int=100, include_plot_twist:bool=False) -&gt; str:
    &#34;&#34;&#34;
    Start a new story.
    &#34;&#34;&#34;
    
    rendering_configs = {
                         &#34;purpose&#34;: self.purpose,
                         &#34;requirements&#34;: requirements,
                         &#34;current_simulation_trace&#34;: self._current_story(),
                         &#34;number_of_words&#34;: number_of_words,
                         &#34;include_plot_twist&#34;: include_plot_twist
                        }

    messages = utils.compose_initial_LLM_messages_with_templates(&#34;story.start.system.mustache&#34;, &#34;story.start.user.mustache&#34;, rendering_configs)
    next_message = openai_utils.client().send_message(messages, temperature=1.5)

    start = next_message[&#34;content&#34;]

    self.current_story += utils.dedent(\
        f&#34;&#34;&#34;

        ## The story begins

        {start}

        &#34;&#34;&#34;
        )

    return start</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="tinytroupe" href="index.html">tinytroupe</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="tinytroupe.story.TinyStory" href="#tinytroupe.story.TinyStory">TinyStory</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.story.TinyStory.continue_story" href="#tinytroupe.story.TinyStory.continue_story">continue_story</a></code></li>
<li><code><a title="tinytroupe.story.TinyStory.start_story" href="#tinytroupe.story.TinyStory.start_story">start_story</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>