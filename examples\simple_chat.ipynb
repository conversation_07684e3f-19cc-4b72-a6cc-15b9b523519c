{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Simple Chat\n", "\n", "A simple demonstration of two agents talking to each other."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import sys\n", "sys.path.insert(0, '..')\n", "\n", "import tinytroupe\n", "from tinytroupe.agent import <PERSON><PERSON><PERSON>\n", "from tinytroupe.environment import TinyWorld, TinySocialNetwork\n", "from tinytroupe.examples import *"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["os.environ[\"OPENAI_API_KEY\"] = \"sk-XAFLlw5V6fOGpk1YYBOXJbdQ2CrvjBDd0pmVCU3tajK6UytK\"\n", "os.environ[\"OPENAI_BASE_URL\"] = \"http://armbian.local:8082/v1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lisa = TinyPerson.load_specification(\"./agents/Lisa.agent.json\")  # <PERSON>, the data scientist\n", "oscar = TinyPerson.load_specification(\"./agents/Oscar.agent.json\")  # <PERSON>, the architect"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["world = TinyWorld(\"Chat Room\", [lisa, oscar])\n", "world.make_everyone_accessible()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lisa.listen(\"Talk to <PERSON> to know more about him\")\n", "world.run(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lisa.pp_current_interactions()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["oscar.pp_current_interactions()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tinytroupe (3.10.18)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}