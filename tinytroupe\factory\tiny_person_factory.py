import os
import json
import chevron

from .tiny_factory import TinyFactory
from tinytroupe.factory import logger
from tinytroupe import openai_utils
from tinytroupe.agent import TinyPerson
import tinytroupe.utils as utils
from tinytroupe.control import transactional

class TinyPersonFactory(TinyFactory):

    def __init__(self, context_text, simulation_id:str=None):
        """
        Initialize a TinyPersonFactory instance.

        Args:
            context_text (str): The context text used to generate the TinyPerson instances.
            simulation_id (str, optional): The ID of the simulation. Defaults to None.
        """
        super().__init__(simulation_id)
        self.person_prompt_template_path = os.path.join(os.path.dirname(__file__), 'prompts/generate_person.mustache')
        self.context_text = context_text
        self.generated_minibios = [] # keep track of the generated persons. We keep the minibio to avoid generating the same person twice.
        self.generated_names = []

    @staticmethod
    def generate_person_factories(number_of_factories, generic_context_text):
        """
        Generate a list of TinyPersonFactory instances using OpenAI's LLM.

        Args:
            number_of_factories (int): The number of TinyPersonFactory instances to generate.
            generic_context_text (str): The generic context text used to generate the TinyPersonFactory instances.

        Returns:
            list: A list of TinyPersonFactory instances.
        """
        
        logger.info(f"Starting the generation of the {number_of_factories} person factories based on that context: {generic_context_text}")
        
        system_prompt = open(os.path.join(os.path.dirname(__file__), 'prompts/generate_person_factory.md')).read()

        messages = []
        messages.append({"role": "system", "content": system_prompt})

        user_prompt = chevron.render("Please, create {{number_of_factories}} person descriptions based on the following broad context: {{context}}", {
            "number_of_factories": number_of_factories,
            "context": generic_context_text
        })

        messages.append({"role": "user", "content": user_prompt})

        response = openai_utils.client().send_message(messages)

        if response is not None:
            result = utils.extract_json(response["content"])

            factories = []
            for i in range(number_of_factories):
                logger.debug(f"Generating person factory with description: {result[i]}")
                factories.append(TinyPersonFactory(result[i]))

            return factories

        return None

    def generate_person(self, 
                        agent_particularities:str=None, 
                        temperature:float=1.5, 
                        frequency_penalty:float=0.0,
                        presence_penalty:float=0.0, 
                        attepmpts:int=10):
        """
        Generate a TinyPerson instance using OpenAI's LLM.

        Args:
            agent_particularities (str): The particularities of the agent.
            temperature (float): The temperature to use when sampling from the LLM.

        Returns:
            TinyPerson: A TinyPerson instance generated using the LLM.
        """

        logger.info(f"Starting the person generation based on that context: {self.context_text}")

    
        # read example specs from files. 
        example_1 = json.load(open(os.path.join(os.path.dirname(__file__), '../examples/agents/Friedrich_Wolf.agent.json')))
        example_2 = json.load(open(os.path.join(os.path.dirname(__file__), '../examples/agents/Sophie_Lefevre.agent.json')))

        # We must include all agent names generated in the whole of the simulation, not only the ones generated by this factory,
        # since they all share the same name space.
        #
        # For the minibios, we only need to keep track of the ones generated by this factory, since they are unique to each factory
        # and are used to guide the sampling process.
        prompt = chevron.render(open(self.person_prompt_template_path).read(), {
            "context": self.context_text,
            "agent_particularities": agent_particularities,
            
            #Note that we need to dump them to JSON strings, to ensure we get double quotes,
            # and other formatting issues are avoided.
            "example_1": json.dumps(example_1["persona"], indent=4),
            "example_2": json.dumps(example_2["persona"], indent=4),

            "already_generated_minibios": self.generated_minibios,
            "already_generated_names": TinyPerson.all_agents_names()
        })

        def aux_generate(attempt):

            messages = []
            messages += [{"role": "system", "content": "You are a system that generates specifications for realistic simulations of people. You follow the generation rules and constraints carefully."},
                        {"role": "user", "content": prompt}]
            
            if attempt > 1:
                # we failed once already due to repetition, so we try to further reinforce the message to avoid repetition.
                messages.append({"role": "user", "content": "IMPORTANT: Please ensure you **do not** generate the same name again. Agent names **must** be unique."+ \
                                                            "Read the list of already generated names to avoid repetition. If necessary, generate a longer name to ensure it is new."})

            # due to a technicality, we need to call an auxiliary method to be able to use the transactional decorator.
            message = self._aux_model_call(messages=messages, 
                                            temperature=temperature,
                                            frequency_penalty=frequency_penalty,
                                            presence_penalty=presence_penalty)

            if message is not None:
                result = utils.extract_json(message["content"])

                logger.debug(f"At attempt {attempt}, generated person parameters:\n{json.dumps(result, indent=4, sort_keys=True)}")

                # only accept the generated spec if the name is not already in the generated names, because they must be unique.
                if result["name"].lower() not in self.generated_names:
                    return result
                else:
                    logger.info(f"Person with name {result['name']} was already generated, cannot be reused.")

            return None # no suitable agent was generated
        
        agent_spec = None
        attempt = 0
        while agent_spec is None and attempt < attepmpts:
            try:
                attempt += 1
                agent_spec = aux_generate(attempt=attempt)
            except Exception as e:
                logger.error(f"Error while generating agent specification: {e}")
        
        # create the fresh agent
        if agent_spec is not None:
            # the agent is created here. This is why the present method cannot be cached. Instead, an auxiliary method is used
            # for the actual model call, so that it gets cached properly without skipping the agent creation.
            person = TinyPerson(agent_spec["name"])
            self._setup_agent(person, agent_spec)
            self.generated_minibios.append(person.minibio())
            self.generated_names.append(person.get("name").lower())
            return person
        else:
            logger.error(f"Could not generate an agent after {attepmpts} attempts.")
            return None

    def generate_people(self, number_of_people:int, 
                        agent_particularities:str=None, 
                        temperature:float=1.5, 
                        frequency_penalty:float=0.0,
                        presence_penalty:float=0.0,
                        attepmpts:int=10, 
                        verbose:bool=False) -> list:
        """
        Generate a list of TinyPerson instances using OpenAI's LLM.

        Args:
            number_of_people (int): The number of TinyPerson instances to generate.
            agent_particularities (str): The particularities of the agent.
            temperature (float): The temperature to use when sampling from the LLM.
            verbose (bool): Whether to print verbose information.

        Returns:
            list: A list of TinyPerson instances generated using the LLM.
        """
        people = []
        for i in range(number_of_people):
            person = self.generate_person(agent_particularities=agent_particularities, 
                                            temperature=temperature, 
                                            frequency_penalty=frequency_penalty,
                                            presence_penalty=presence_penalty,
                                            attepmpts=attepmpts)
            if person is not None:
                people.append(person)
                info_msg = f"Generated person {i+1}/{number_of_people}: {person.minibio()}"
                logger.info(info_msg)
                if verbose:
                    print(info_msg)
            else:
                logger.error(f"Could not generate person {i+1}/{number_of_people}.")

        return people
    
    @transactional
    def _aux_model_call(self, messages, temperature, frequency_penalty, presence_penalty):
        """
        Auxiliary method to make a model call. This is needed in order to be able to use the transactional decorator,
        due too a technicality - otherwise, the agent creation would be skipped during cache reutilization, and
        we don't want that.
        """
        return openai_utils.client().send_message(messages, 
                                                  temperature=temperature, 
                                                  frequency_penalty=frequency_penalty, 
                                                  presence_penalty=presence_penalty,
                                                  response_format={"type": "json_object"})
    
    @transactional
    def _setup_agent(self, agent, configuration):
        """
        Sets up the agent with the necessary elements.
        """
        agent.include_persona_definitions(configuration)
        
        # does not return anything, as we don't want to cache the agent object itself.
    
