#!/usr/bin/env python3
"""
Test script to verify the fixes for TinyTroupe OpenAI API compatibility issues.
"""

import sys
import os
import logging

# Add the tinytroupe directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'tinytroupe'))

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

try:
    from tinytroupe.agent import TinyPerson
    from tinytroupe.environment import TinyWorld
    from tinytroupe import openai_utils
    
    print("✓ Successfully imported TinyTroupe modules")
    
    # Test 1: Check if we can create a TinyPerson without errors
    print("\n=== Test 1: Creating TinyPerson ===")
    try:
        person = TinyPerson("TestPerson")
        person.define("age", 25)
        person.define("occupation", "Software Developer")
        person.define("personality", "Friendly and helpful")
        print("✓ Successfully created TinyPerson")
    except Exception as e:
        print(f"✗ Failed to create TinyPerson: {e}")
        
    # Test 2: Test the validation function
    print("\n=== Test 2: Testing validation function ===")
    try:
        # Test with valid content
        valid_content = {
            "action": {"type": "TALK", "content": "Hello", "target": ""},
            "cognitive_state": {"goals": "Test", "attention": "Testing", "emotions": "Happy"}
        }
        validated = person._validate_cognitive_action_content(valid_content)
        print("✓ Validation works with valid content")
        
        # Test with invalid content
        invalid_content = {"invalid": "data"}
        validated = person._validate_cognitive_action_content(invalid_content)
        print("✓ Validation works with invalid content (provides defaults)")
        
        # Test with empty content
        empty_content = {}
        validated = person._validate_cognitive_action_content(empty_content)
        print("✓ Validation works with empty content (provides defaults)")
        
    except Exception as e:
        print(f"✗ Validation function failed: {e}")
    
    # Test 3: Test OpenAI client initialization
    print("\n=== Test 3: Testing OpenAI client ===")
    try:
        client = openai_utils.client()
        print("✓ Successfully initialized OpenAI client")
        print(f"  Client type: {type(client)}")
    except Exception as e:
        print(f"✗ Failed to initialize OpenAI client: {e}")
    
    # Test 4: Test a simple interaction (this might fail with API errors, but shouldn't crash)
    print("\n=== Test 4: Testing simple interaction ===")
    try:
        world = TinyWorld("TestWorld", [person])
        
        # Try a simple action - this might fail due to API issues but shouldn't crash
        person.listen("Hello, how are you?")
        print("✓ Successfully processed listen command")
        
        # Try to get the person to act - this is where the original errors occurred
        try:
            person.act()
            print("✓ Successfully executed act() without crashing")
        except Exception as e:
            print(f"⚠ act() failed (expected with API issues): {e}")
            # This is expected if there are still API connectivity issues
            
    except Exception as e:
        print(f"✗ Simple interaction test failed: {e}")
    
    print("\n=== Test Summary ===")
    print("If you see mostly ✓ marks above, the fixes are working correctly.")
    print("API-related failures (⚠) are expected if there are connectivity issues,")
    print("but the code should no longer crash with JSON validation or 'stream' errors.")
    
except ImportError as e:
    print(f"✗ Failed to import TinyTroupe modules: {e}")
    print("Make sure you're running this from the TinyTroupe root directory")
except Exception as e:
    print(f"✗ Unexpected error: {e}")
