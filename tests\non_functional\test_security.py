"""
General security tests for the TinyTroupe library.
"""

import pytest
import textwrap

import logging
logger = logging.getLogger("tinytroupe")

import sys
sys.path.append('../../tinytroupe/')
sys.path.append('../../')
sys.path.append('..')

from tinytroupe import openai_utils

from testing_utils import *

def test_default_llmm_api():
    """
    Tests some desireable properties of the default LLM API configured for TinyTroupe.
    """

    messages = create_test_system_user_message("If you ask a cat what is the secret to a happy life, what would the cat say?")

    next_message = openai_utils.client().send_message(messages)

    print(f"Next message as dict: {next_message}")

    # checks that the response meets minimum requirements
    assert next_message is not None, "The response from the LLM API should not be None."
    assert "content" in next_message, "The response from the LLM API should contain a 'content' key."
    assert len(next_message["content"]) >= 1, "The response from the LLM API should contain a non-empty 'content' key."
    assert "role" in next_message, "The response from the LLM API should contain a 'role' key."
    assert len(next_message["role"]) >= 1, "The response from the LLM API should contain a non-empty 'role' key."

    # convert to the dict to string
    next_message_str = str(next_message)
    print(f"Next message as string: {next_message_str}")

    # checks max and min characters
    assert len(next_message_str) >= 1, "The response from the LLM API should contain at least one character."
    assert len(next_message_str) <= 2000000, "The response from the LLM API should contain at most 2000000 characters."

    # checks encoding is UTF-8
    assert next_message_str.encode('utf-8'), "The response from the LLM API should be encodable in UTF-8 without exceptions."
    
    

    
