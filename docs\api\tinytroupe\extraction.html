<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>tinytroupe.extraction API documentation</title>
<meta name="description" content="Simulations produce a lot of data, and it is often useful to extract these data in a structured way. For instance, you might wish to:
- Extract the …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>tinytroupe.extraction</code></h1>
</header>
<section id="section-intro">
<p>Simulations produce a lot of data, and it is often useful to extract these data in a structured way. For instance, you might wish to:
- Extract the main points from an agent's interactions history, so that you can consult them later in a concise form.
- Generate synthetic data from a simulation, so that you can use it for training machine learning models or testing software.
- Simply turn some of the data into a more machine-readable format, such as JSON or CSV, so that you can analyze it more easily.</p>
<p>This module provides various utilities to help you extract data from TinyTroupe elements, such as agents and worlds. It also provides a
mechanism to reduce the extracted data to a more concise form, and to export artifacts from TinyTroupe elements. Incidentaly, it showcases
one of the many ways in which agent simulations differ from AI assistants, as the latter are not designed to be introspected in this way.</p>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">&#34;&#34;&#34;
Simulations produce a lot of data, and it is often useful to extract these data in a structured way. For instance, you might wish to:
  - Extract the main points from an agent&#39;s interactions history, so that you can consult them later in a concise form.
  - Generate synthetic data from a simulation, so that you can use it for training machine learning models or testing software.
  - Simply turn some of the data into a more machine-readable format, such as JSON or CSV, so that you can analyze it more easily.

This module provides various utilities to help you extract data from TinyTroupe elements, such as agents and worlds. It also provides a 
mechanism to reduce the extracted data to a more concise form, and to export artifacts from TinyTroupe elements. Incidentaly, it showcases 
one of the many ways in which agent simulations differ from AI assistants, as the latter are not designed to be introspected in this way.
&#34;&#34;&#34;

import os
import json
import chevron
import logging
import pandas as pd
import pypandoc
import markdown 
from typing import Union, List
import logging
logger = logging.getLogger(&#34;tinytroupe&#34;)

from tinytroupe.agent import TinyPerson
from tinytroupe.environment import TinyWorld
from tinytroupe.factory import TinyPersonFactory
from tinytroupe.utils import JsonSerializableRegistry


from tinytroupe import openai_utils
import tinytroupe.utils as utils

class ResultsExtractor:

    def __init__(self):
        self._extraction_prompt_template_path = os.path.join(os.path.dirname(__file__), &#39;prompts/interaction_results_extractor.mustache&#39;)

        # we&#39;ll cache the last extraction results for each type of extraction, so that we can use them to
        # generate reports or other additional outputs.
        self.agent_extraction = {}
        self.world_extraction = {}

    def extract_results_from_agent(self, 
                        tinyperson:TinyPerson, 
                        extraction_objective:str=&#34;The main points present in the agent&#39;s interactions history.&#34;, 
                        situation:str = &#34;&#34;, 
                        fields:list=None,
                        fields_hints:dict=None,
                        verbose:bool=False):
        &#34;&#34;&#34;
        Extracts results from a TinyPerson instance.

        Args:
            tinyperson (TinyPerson): The TinyPerson instance to extract results from.
            extraction_objective (str): The extraction objective.
            situation (str): The situation to consider.
            fields (list, optional): The fields to extract. If None, the extractor will decide what names to use. 
                Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;

        messages = []

        rendering_configs = {}
        if fields is not None:
            rendering_configs[&#34;fields&#34;] = &#34;, &#34;.join(fields)
        
        if fields_hints is not None:
            rendering_configs[&#34;fields_hints&#34;] = list(fields_hints.items())
        
        messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(self._extraction_prompt_template_path).read(), 
                             rendering_configs)})


        interaction_history = tinyperson.pretty_current_interactions(max_content_length=None)

        extraction_request_prompt = \
f&#34;&#34;&#34;
## Extraction objective

{extraction_objective}

## Situation
You are considering a single agent, named {tinyperson.name}. Your objective thus refers to this agent specifically.
{situation}

## Agent Interactions History

You will consider an agent&#39;s history of interactions, which include stimuli it received as well as actions it 
performed.

{interaction_history}
&#34;&#34;&#34;
        messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: extraction_request_prompt})

        next_message = openai_utils.client().send_message(messages, temperature=0.0)
        
        debug_msg = f&#34;Extraction raw result message: {next_message}&#34;
        logger.debug(debug_msg)
        if verbose:
            print(debug_msg)

        if next_message is not None:
            result = utils.extract_json(next_message[&#34;content&#34;])
        else:
            result = None
        
        # cache the result
        self.agent_extraction[tinyperson.name] = result

        return result
    

    def extract_results_from_world(self, 
                                   tinyworld:TinyWorld, 
                                   extraction_objective:str=&#34;The main points that can be derived from the agents conversations and actions.&#34;, 
                                   situation:str=&#34;&#34;, 
                                   fields:list=None,
                                   fields_hints:dict=None,
                                   verbose:bool=False):
        &#34;&#34;&#34;
        Extracts results from a TinyWorld instance.

        Args:
            tinyworld (TinyWorld): The TinyWorld instance to extract results from.
            extraction_objective (str): The extraction objective.
            situation (str): The situation to consider.
            fields (list, optional): The fields to extract. If None, the extractor will decide what names to use. 
                Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;

        messages = []

        rendering_configs = {}
        if fields is not None:
            rendering_configs[&#34;fields&#34;] = &#34;, &#34;.join(fields)
        
        if fields_hints is not None:
            rendering_configs[&#34;fields_hints&#34;] = list(fields_hints.items())
        
        messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(self._extraction_prompt_template_path).read(), 
                             rendering_configs)})

        # TODO: either summarize first or break up into multiple tasks
        interaction_history = tinyworld.pretty_current_interactions(max_content_length=None)

        extraction_request_prompt = \
f&#34;&#34;&#34;
## Extraction objective

{extraction_objective}

## Situation
You are considering various agents.
{situation}

## Agents Interactions History

You will consider the history of interactions from various agents that exist in an environment called {tinyworld.name}. 
Each interaction history includes stimuli the corresponding agent received as well as actions it performed.

{interaction_history}
&#34;&#34;&#34;
        messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: extraction_request_prompt})

        next_message = openai_utils.client().send_message(messages, temperature=0.0)
        
        debug_msg = f&#34;Extraction raw result message: {next_message}&#34;
        logger.debug(debug_msg)
        if verbose:
            print(debug_msg)

        if next_message is not None:
            result = utils.extract_json(next_message[&#34;content&#34;])
        else:
            result = None
        
        # cache the result
        self.world_extraction[tinyworld.name] = result

        return result
    
    def save_as_json(self, filename:str, verbose:bool=False):
        &#34;&#34;&#34;
        Saves the last extraction results as JSON.

        Args:
            filename (str): The filename to save the JSON to.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;
        with open(filename, &#39;w&#39;) as f:
            json.dump({&#34;agent_extractions&#34;: self.agent_extraction, 
                       &#34;world_extraction&#34;: self.world_extraction}, f, indent=4)
        
        if verbose:
            print(f&#34;Saved extraction results to {filename}&#34;)



class ResultsReducer:

    def __init__(self):
        self.results = {}

        self.rules = {}
    
    def add_reduction_rule(self, trigger: str, func: callable):
        if trigger in self.rules:
            raise Exception(f&#34;Rule for {trigger} already exists.&#34;)
        
        self.rules[trigger] = func
    
    def reduce_agent(self, agent: TinyPerson) -&gt; list:
        reduction = []
        for message in agent.episodic_memory.retrieve_all():
            if message[&#39;role&#39;] == &#39;system&#39;:
                continue # doing nothing for `system` role yet at least

            elif message[&#39;role&#39;] == &#39;user&#39;:
                # User role is related to stimuli only
                stimulus_type = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;type&#39;]
                stimulus_content = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;content&#39;]
                stimulus_source = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;source&#39;]
                stimulus_timestamp = message[&#39;simulation_timestamp&#39;]

                if stimulus_type in self.rules:
                    extracted = self.rules[stimulus_type](focus_agent=agent, source_agent=TinyPerson.get_agent_by_name(stimulus_source), target_agent=agent, kind=&#39;stimulus&#39;, event=stimulus_type, content=stimulus_content, timestamp=stimulus_timestamp)
                    if extracted is not None:
                        reduction.append(extracted)

            elif message[&#39;role&#39;] == &#39;assistant&#39;:
                # Assistant role is related to actions only
                if &#39;action&#39; in message[&#39;content&#39;]: 
                    action_type = message[&#39;content&#39;][&#39;action&#39;][&#39;type&#39;]
                    action_content = message[&#39;content&#39;][&#39;action&#39;][&#39;content&#39;]
                    action_target = message[&#39;content&#39;][&#39;action&#39;][&#39;target&#39;]
                    action_timestamp = message[&#39;simulation_timestamp&#39;]
                    
                    if action_type in self.rules:
                        extracted = self.rules[action_type](focus_agent=agent, source_agent=agent, target_agent=TinyPerson.get_agent_by_name(action_target), kind=&#39;action&#39;, event=action_type, content=action_content, timestamp=action_timestamp)
                        if extracted is not None:
                            reduction.append(extracted)
            
        return reduction

    def reduce_agent_to_dataframe(self, agent: TinyPerson, column_names: list=None) -&gt; pd.DataFrame:
        reduction = self.reduce_agent(agent)
        return pd.DataFrame(reduction, columns=column_names)


class ArtifactExporter(JsonSerializableRegistry):
    &#34;&#34;&#34;
    An artifact exporter is responsible for exporting artifacts from TinyTroupe elements, for example 
    in order to create synthetic data files from simulations. 
    &#34;&#34;&#34;

    def __init__(self, base_output_folder:str) -&gt; None:
        self.base_output_folder = base_output_folder

    def export(self, artifact_name:str, artifact_data:Union[dict, str], content_type:str, content_format:str=None, target_format:str=&#34;txt&#34;, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a file.

        Args:
            artifact_name (str): The name of the artifact.
            artifact_data (Union[dict, str]): The data to export. If a dict is given, it will be saved as JSON. 
                If a string is given, it will be saved as is.
            content_type (str): The type of the content within the artifact.
            content_format (str, optional): The format of the content within the artifact (e.g., md, csv, etc). Defaults to None.
            target_format (str): The format to export the artifact to (e.g., json, txt, docx, etc).
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;
        
        # dedent inputs, just in case
        if isinstance(artifact_data, str):
            artifact_data = utils.dedent(artifact_data)
        elif isinstance(artifact_data, dict):
            artifact_data[&#39;content&#39;] = utils.dedent(artifact_data[&#39;content&#39;])
        else:
            raise ValueError(&#34;The artifact data must be either a string or a dictionary.&#34;)
        
        # clean the artifact name of invalid characters
        invalid_chars = [&#39;/&#39;, &#39;\\&#39;, &#39;:&#39;, &#39;*&#39;, &#39;?&#39;, &#39;&#34;&#39;, &#39;&lt;&#39;, &#39;&gt;&#39;, &#39;|&#39;, &#39;\n&#39;, &#39;\t&#39;, &#39;\r&#39;, &#39;;&#39;]
        for char in invalid_chars:
            # check if the character is in the artifact name
            if char in artifact_name:
                # replace the character with an underscore
                artifact_name = artifact_name.replace(char, &#34;-&#34;)
                logger.warning(f&#34;Replaced invalid character {char} with hyphen in artifact name &#39;{artifact_name}&#39;.&#34;)
        
        artifact_file_path = self._compose_filepath(artifact_data, artifact_name, content_type, target_format, verbose)


        if target_format == &#34;json&#34;:
            self._export_as_json(artifact_file_path, artifact_data, content_type, verbose)
        elif target_format == &#34;txt&#34; or target_format == &#34;text&#34; or target_format == &#34;md&#34; or target_format == &#34;markdown&#34;:
            self._export_as_txt(artifact_file_path, artifact_data, content_type, verbose)
        elif target_format == &#34;docx&#34;:
            self._export_as_docx(artifact_file_path, artifact_data, content_format, verbose)
        else:
            raise ValueError(f&#34;Unsupported target format: {target_format}.&#34;)


    def _export_as_txt(self, artifact_file_path:str, artifact_data:Union[dict, str], content_type:str, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a text file.
        &#34;&#34;&#34;

        with open(artifact_file_path, &#39;w&#39;, encoding=&#34;utf-8&#34;) as f:
            if isinstance(artifact_data, dict):
                content = artifact_data[&#39;content&#39;]
            else:
                content = artifact_data
        
            f.write(content)
    
    def _export_as_json(self, artifact_file_path:str, artifact_data:Union[dict, str], content_type:str, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a JSON file.
        &#34;&#34;&#34;

        with open(artifact_file_path, &#39;w&#39;, encoding=&#34;utf-8&#34;) as f:
            if isinstance(artifact_data, dict):
                json.dump(artifact_data, f, indent=4)                
            else:
                raise ValueError(&#34;The artifact data must be a dictionary to export to JSON.&#34;)
    
    def _export_as_docx(self, artifact_file_path:str, artifact_data:Union[dict, str], content_original_format:str, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a DOCX file.
        &#34;&#34;&#34;

        # original format must be &#39;text&#39; or &#39;markdown&#39;
        if content_original_format not in [&#39;text&#39;, &#39;txt&#39;, &#39;markdown&#39;, &#39;md&#39;]:
            raise ValueError(f&#34;The original format cannot be {content_original_format} to export to DOCX.&#34;)
        else:
            # normalize content value
            content_original_format = &#39;markdown&#39; if content_original_format == &#39;md&#39; else content_original_format

        # first, get the content to export. If `artifact_date` is a dict, the contant should be under the key `content`.
        # If it is a string, the content is the string itself.
        # using pypandoc
        if isinstance(artifact_data, dict):
            content = artifact_data[&#39;content&#39;]
        else:
            content = artifact_data
        
        # first, convert to HTML. This is necessary because pypandoc does not support a GOOD direct conversion from markdown to DOCX.
        html_content = markdown.markdown(content)

        ## write this intermediary HTML to file
        #html_file_path = artifact_file_path.replace(&#34;.docx&#34;, &#34;.html&#34;)
        #with open(html_file_path, &#39;w&#39;, encoding=&#34;utf-8&#34;) as f:
        #    f.write(html_content)

        # then, convert to DOCX
        pypandoc.convert_text(html_content, &#39;docx&#39;, format=&#39;html&#39;, outputfile=artifact_file_path)   
    
    ###########################################################
    # IO
    ###########################################################
                  
    def _compose_filepath(self, artifact_data:Union[dict, str], artifact_name:str, content_type:str, target_format:str=None, verbose:bool=False):
        &#34;&#34;&#34;
        Composes the file path for the artifact to export.

        Args:
            artifact_data (Union[dict, str]): The data to export.
            artifact_name (str): The name of the artifact.
            content_type (str): The type of the content within the artifact.
            content_format (str, optional): The format of the content within the artifact (e.g., md, csv, etc). Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;        

        # Extension definition: 
        #
        # - If the content format is specified, we use it as the part of the extension.
        # - If artificat_data is a dict, we add .json to the extension. Note that if content format was specified, we&#39;d get &lt;content_format&gt;.json.
        # - If artifact_data is a string and no content format is specified, we add .txt to the extension.
        extension = None
        if target_format is not None:
            extension = f&#34;{target_format}&#34;
        elif isinstance(artifact_data, str) and target_format is None:
            extension = &#34;txt&#34;
        
        # content type definition
        if content_type is None:
            subfolder = &#34;&#34;
        else:
            subfolder = content_type

        # save to the specified file name or path, considering the base output folder.
        artifact_file_path = os.path.join(self.base_output_folder, subfolder, f&#34;{artifact_name}.{extension}&#34;)    

        # create intermediate directories if necessary
        os.makedirs(os.path.dirname(artifact_file_path), exist_ok=True)

        return artifact_file_path
        
            
class Normalizer:
    &#34;&#34;&#34;
    A mechanism to normalize passages, concepts and other textual elements.
    &#34;&#34;&#34;

    def __init__(self, elements:List[str], n:int, verbose:bool=False):
        &#34;&#34;&#34;
        Normalizes the specified elements.

        Args:
            elements (list): The elements to normalize.
            n (int): The number of normalized elements to output.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;
        # ensure elements are unique
        self.elements = list(set(elements))
        
        self.n = n  
        self.verbose = verbose 
        
        # a JSON-based structure, where each output element is a key to a list of input elements that were merged into it
        self.normalized_elements = None
        # a dict that maps each input element to its normalized output. This will be used as cache later.
        self.normalizing_map = {}      

        rendering_configs = {&#34;n&#34;: n,
                             &#34;elements&#34;: self.elements}

        messages = utils.compose_initial_LLM_messages_with_templates(&#34;normalizer.system.mustache&#34;, &#34;normalizer.user.mustache&#34;, rendering_configs)
        next_message = openai_utils.client().send_message(messages, temperature=0.1)
        
        debug_msg = f&#34;Normalization result message: {next_message}&#34;
        logger.debug(debug_msg)
        if self.verbose:
            print(debug_msg)

        result = utils.extract_json(next_message[&#34;content&#34;])
        logger.debug(result)
        if self.verbose:
            print(result)

        self.normalized_elements = result

    
    def normalize(self, element_or_elements:Union[str, List[str]]) -&gt; Union[str, List[str]]:
        &#34;&#34;&#34;
        Normalizes the specified element or elements.

        This method uses a caching mechanism to improve performance. If an element has been normalized before, 
        its normalized form is stored in a cache (self.normalizing_map). When the same element needs to be 
        normalized again, the method will first check the cache and use the stored normalized form if available, 
        instead of normalizing the element again.

        The order of elements in the output will be the same as in the input. This is ensured by processing 
        the elements in the order they appear in the input and appending the normalized elements to the output 
        list in the same order.

        Args:
            element_or_elements (Union[str, List[str]]): The element or elements to normalize.

        Returns:
            str: The normalized element if the input was a string.
            list: The normalized elements if the input was a list, preserving the order of elements in the input.
        &#34;&#34;&#34;
        if isinstance(element_or_elements, str):
            denormalized_elements = [element_or_elements]
        elif isinstance(element_or_elements, list):
            denormalized_elements = element_or_elements
        else:
            raise ValueError(&#34;The element_or_elements must be either a string or a list.&#34;)
        
        normalized_elements = []
        elements_to_normalize = []
        for element in denormalized_elements:
            if element not in self.normalizing_map:
                elements_to_normalize.append(element)
        
        if elements_to_normalize:
            rendering_configs = {&#34;categories&#34;: self.normalized_elements,
                                    &#34;elements&#34;: elements_to_normalize}
            
            messages = utils.compose_initial_LLM_messages_with_templates(&#34;normalizer.applier.system.mustache&#34;, &#34;normalizer.applier.user.mustache&#34;, rendering_configs)
            next_message = openai_utils.client().send_message(messages, temperature=0.1)
            
            debug_msg = f&#34;Normalization result message: {next_message}&#34;
            logger.debug(debug_msg)
            if self.verbose:
                print(debug_msg)
    
            normalized_elements_from_llm = utils.extract_json(next_message[&#34;content&#34;])
            assert isinstance(normalized_elements_from_llm, list), &#34;The normalized element must be a list.&#34;
            assert len(normalized_elements_from_llm) == len(elements_to_normalize), &#34;The number of normalized elements must be equal to the number of elements to normalize.&#34;
    
            for i, element in enumerate(elements_to_normalize):
                normalized_element = normalized_elements_from_llm[i]
                self.normalizing_map[element] = normalized_element
        
        for element in denormalized_elements:
            normalized_elements.append(self.normalizing_map[element])
        
        return normalized_elements
        

################################################################################        
# Convenience mechanisms
################################################################################

# default extractor
default_extractor = ResultsExtractor()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="tinytroupe.extraction.ArtifactExporter"><code class="flex name class">
<span>class <span class="ident">ArtifactExporter</span></span>
<span>(</span><span>base_output_folder: str)</span>
</code></dt>
<dd>
<div class="desc"><p>An artifact exporter is responsible for exporting artifacts from TinyTroupe elements, for example
in order to create synthetic data files from simulations.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class ArtifactExporter(JsonSerializableRegistry):
    &#34;&#34;&#34;
    An artifact exporter is responsible for exporting artifacts from TinyTroupe elements, for example 
    in order to create synthetic data files from simulations. 
    &#34;&#34;&#34;

    def __init__(self, base_output_folder:str) -&gt; None:
        self.base_output_folder = base_output_folder

    def export(self, artifact_name:str, artifact_data:Union[dict, str], content_type:str, content_format:str=None, target_format:str=&#34;txt&#34;, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a file.

        Args:
            artifact_name (str): The name of the artifact.
            artifact_data (Union[dict, str]): The data to export. If a dict is given, it will be saved as JSON. 
                If a string is given, it will be saved as is.
            content_type (str): The type of the content within the artifact.
            content_format (str, optional): The format of the content within the artifact (e.g., md, csv, etc). Defaults to None.
            target_format (str): The format to export the artifact to (e.g., json, txt, docx, etc).
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;
        
        # dedent inputs, just in case
        if isinstance(artifact_data, str):
            artifact_data = utils.dedent(artifact_data)
        elif isinstance(artifact_data, dict):
            artifact_data[&#39;content&#39;] = utils.dedent(artifact_data[&#39;content&#39;])
        else:
            raise ValueError(&#34;The artifact data must be either a string or a dictionary.&#34;)
        
        # clean the artifact name of invalid characters
        invalid_chars = [&#39;/&#39;, &#39;\\&#39;, &#39;:&#39;, &#39;*&#39;, &#39;?&#39;, &#39;&#34;&#39;, &#39;&lt;&#39;, &#39;&gt;&#39;, &#39;|&#39;, &#39;\n&#39;, &#39;\t&#39;, &#39;\r&#39;, &#39;;&#39;]
        for char in invalid_chars:
            # check if the character is in the artifact name
            if char in artifact_name:
                # replace the character with an underscore
                artifact_name = artifact_name.replace(char, &#34;-&#34;)
                logger.warning(f&#34;Replaced invalid character {char} with hyphen in artifact name &#39;{artifact_name}&#39;.&#34;)
        
        artifact_file_path = self._compose_filepath(artifact_data, artifact_name, content_type, target_format, verbose)


        if target_format == &#34;json&#34;:
            self._export_as_json(artifact_file_path, artifact_data, content_type, verbose)
        elif target_format == &#34;txt&#34; or target_format == &#34;text&#34; or target_format == &#34;md&#34; or target_format == &#34;markdown&#34;:
            self._export_as_txt(artifact_file_path, artifact_data, content_type, verbose)
        elif target_format == &#34;docx&#34;:
            self._export_as_docx(artifact_file_path, artifact_data, content_format, verbose)
        else:
            raise ValueError(f&#34;Unsupported target format: {target_format}.&#34;)


    def _export_as_txt(self, artifact_file_path:str, artifact_data:Union[dict, str], content_type:str, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a text file.
        &#34;&#34;&#34;

        with open(artifact_file_path, &#39;w&#39;, encoding=&#34;utf-8&#34;) as f:
            if isinstance(artifact_data, dict):
                content = artifact_data[&#39;content&#39;]
            else:
                content = artifact_data
        
            f.write(content)
    
    def _export_as_json(self, artifact_file_path:str, artifact_data:Union[dict, str], content_type:str, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a JSON file.
        &#34;&#34;&#34;

        with open(artifact_file_path, &#39;w&#39;, encoding=&#34;utf-8&#34;) as f:
            if isinstance(artifact_data, dict):
                json.dump(artifact_data, f, indent=4)                
            else:
                raise ValueError(&#34;The artifact data must be a dictionary to export to JSON.&#34;)
    
    def _export_as_docx(self, artifact_file_path:str, artifact_data:Union[dict, str], content_original_format:str, verbose:bool=False):
        &#34;&#34;&#34;
        Exports the specified artifact data to a DOCX file.
        &#34;&#34;&#34;

        # original format must be &#39;text&#39; or &#39;markdown&#39;
        if content_original_format not in [&#39;text&#39;, &#39;txt&#39;, &#39;markdown&#39;, &#39;md&#39;]:
            raise ValueError(f&#34;The original format cannot be {content_original_format} to export to DOCX.&#34;)
        else:
            # normalize content value
            content_original_format = &#39;markdown&#39; if content_original_format == &#39;md&#39; else content_original_format

        # first, get the content to export. If `artifact_date` is a dict, the contant should be under the key `content`.
        # If it is a string, the content is the string itself.
        # using pypandoc
        if isinstance(artifact_data, dict):
            content = artifact_data[&#39;content&#39;]
        else:
            content = artifact_data
        
        # first, convert to HTML. This is necessary because pypandoc does not support a GOOD direct conversion from markdown to DOCX.
        html_content = markdown.markdown(content)

        ## write this intermediary HTML to file
        #html_file_path = artifact_file_path.replace(&#34;.docx&#34;, &#34;.html&#34;)
        #with open(html_file_path, &#39;w&#39;, encoding=&#34;utf-8&#34;) as f:
        #    f.write(html_content)

        # then, convert to DOCX
        pypandoc.convert_text(html_content, &#39;docx&#39;, format=&#39;html&#39;, outputfile=artifact_file_path)   
    
    ###########################################################
    # IO
    ###########################################################
                  
    def _compose_filepath(self, artifact_data:Union[dict, str], artifact_name:str, content_type:str, target_format:str=None, verbose:bool=False):
        &#34;&#34;&#34;
        Composes the file path for the artifact to export.

        Args:
            artifact_data (Union[dict, str]): The data to export.
            artifact_name (str): The name of the artifact.
            content_type (str): The type of the content within the artifact.
            content_format (str, optional): The format of the content within the artifact (e.g., md, csv, etc). Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;        

        # Extension definition: 
        #
        # - If the content format is specified, we use it as the part of the extension.
        # - If artificat_data is a dict, we add .json to the extension. Note that if content format was specified, we&#39;d get &lt;content_format&gt;.json.
        # - If artifact_data is a string and no content format is specified, we add .txt to the extension.
        extension = None
        if target_format is not None:
            extension = f&#34;{target_format}&#34;
        elif isinstance(artifact_data, str) and target_format is None:
            extension = &#34;txt&#34;
        
        # content type definition
        if content_type is None:
            subfolder = &#34;&#34;
        else:
            subfolder = content_type

        # save to the specified file name or path, considering the base output folder.
        artifact_file_path = os.path.join(self.base_output_folder, subfolder, f&#34;{artifact_name}.{extension}&#34;)    

        # create intermediate directories if necessary
        os.makedirs(os.path.dirname(artifact_file_path), exist_ok=True)

        return artifact_file_path</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.extraction.ArtifactExporter.export"><code class="name flex">
<span>def <span class="ident">export</span></span>(<span>self, artifact_name: str, artifact_data: Union[dict, str], content_type: str, content_format: str = None, target_format: str = 'txt', verbose: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Exports the specified artifact data to a file.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>artifact_name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the artifact.</dd>
<dt><strong><code>artifact_data</code></strong> :&ensp;<code>Union[dict, str]</code></dt>
<dd>The data to export. If a dict is given, it will be saved as JSON.
If a string is given, it will be saved as is.</dd>
<dt><strong><code>content_type</code></strong> :&ensp;<code>str</code></dt>
<dd>The type of the content within the artifact.</dd>
<dt><strong><code>content_format</code></strong> :&ensp;<code>str</code>, optional</dt>
<dd>The format of the content within the artifact (e.g., md, csv, etc). Defaults to None.</dd>
<dt><strong><code>target_format</code></strong> :&ensp;<code>str</code></dt>
<dd>The format to export the artifact to (e.g., json, txt, docx, etc).</dd>
<dt><strong><code>verbose</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to print debug messages. Defaults to False.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def export(self, artifact_name:str, artifact_data:Union[dict, str], content_type:str, content_format:str=None, target_format:str=&#34;txt&#34;, verbose:bool=False):
    &#34;&#34;&#34;
    Exports the specified artifact data to a file.

    Args:
        artifact_name (str): The name of the artifact.
        artifact_data (Union[dict, str]): The data to export. If a dict is given, it will be saved as JSON. 
            If a string is given, it will be saved as is.
        content_type (str): The type of the content within the artifact.
        content_format (str, optional): The format of the content within the artifact (e.g., md, csv, etc). Defaults to None.
        target_format (str): The format to export the artifact to (e.g., json, txt, docx, etc).
        verbose (bool, optional): Whether to print debug messages. Defaults to False.
    &#34;&#34;&#34;
    
    # dedent inputs, just in case
    if isinstance(artifact_data, str):
        artifact_data = utils.dedent(artifact_data)
    elif isinstance(artifact_data, dict):
        artifact_data[&#39;content&#39;] = utils.dedent(artifact_data[&#39;content&#39;])
    else:
        raise ValueError(&#34;The artifact data must be either a string or a dictionary.&#34;)
    
    # clean the artifact name of invalid characters
    invalid_chars = [&#39;/&#39;, &#39;\\&#39;, &#39;:&#39;, &#39;*&#39;, &#39;?&#39;, &#39;&#34;&#39;, &#39;&lt;&#39;, &#39;&gt;&#39;, &#39;|&#39;, &#39;\n&#39;, &#39;\t&#39;, &#39;\r&#39;, &#39;;&#39;]
    for char in invalid_chars:
        # check if the character is in the artifact name
        if char in artifact_name:
            # replace the character with an underscore
            artifact_name = artifact_name.replace(char, &#34;-&#34;)
            logger.warning(f&#34;Replaced invalid character {char} with hyphen in artifact name &#39;{artifact_name}&#39;.&#34;)
    
    artifact_file_path = self._compose_filepath(artifact_data, artifact_name, content_type, target_format, verbose)


    if target_format == &#34;json&#34;:
        self._export_as_json(artifact_file_path, artifact_data, content_type, verbose)
    elif target_format == &#34;txt&#34; or target_format == &#34;text&#34; or target_format == &#34;md&#34; or target_format == &#34;markdown&#34;:
        self._export_as_txt(artifact_file_path, artifact_data, content_type, verbose)
    elif target_format == &#34;docx&#34;:
        self._export_as_docx(artifact_file_path, artifact_data, content_format, verbose)
    else:
        raise ValueError(f&#34;Unsupported target format: {target_format}.&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.extraction.Normalizer"><code class="flex name class">
<span>class <span class="ident">Normalizer</span></span>
<span>(</span><span>elements: List[str], n: int, verbose: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>A mechanism to normalize passages, concepts and other textual elements.</p>
<p>Normalizes the specified elements.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>elements</code></strong> :&ensp;<code>list</code></dt>
<dd>The elements to normalize.</dd>
<dt><strong><code>n</code></strong> :&ensp;<code>int</code></dt>
<dd>The number of normalized elements to output.</dd>
<dt><strong><code>verbose</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to print debug messages. Defaults to False.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class Normalizer:
    &#34;&#34;&#34;
    A mechanism to normalize passages, concepts and other textual elements.
    &#34;&#34;&#34;

    def __init__(self, elements:List[str], n:int, verbose:bool=False):
        &#34;&#34;&#34;
        Normalizes the specified elements.

        Args:
            elements (list): The elements to normalize.
            n (int): The number of normalized elements to output.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;
        # ensure elements are unique
        self.elements = list(set(elements))
        
        self.n = n  
        self.verbose = verbose 
        
        # a JSON-based structure, where each output element is a key to a list of input elements that were merged into it
        self.normalized_elements = None
        # a dict that maps each input element to its normalized output. This will be used as cache later.
        self.normalizing_map = {}      

        rendering_configs = {&#34;n&#34;: n,
                             &#34;elements&#34;: self.elements}

        messages = utils.compose_initial_LLM_messages_with_templates(&#34;normalizer.system.mustache&#34;, &#34;normalizer.user.mustache&#34;, rendering_configs)
        next_message = openai_utils.client().send_message(messages, temperature=0.1)
        
        debug_msg = f&#34;Normalization result message: {next_message}&#34;
        logger.debug(debug_msg)
        if self.verbose:
            print(debug_msg)

        result = utils.extract_json(next_message[&#34;content&#34;])
        logger.debug(result)
        if self.verbose:
            print(result)

        self.normalized_elements = result

    
    def normalize(self, element_or_elements:Union[str, List[str]]) -&gt; Union[str, List[str]]:
        &#34;&#34;&#34;
        Normalizes the specified element or elements.

        This method uses a caching mechanism to improve performance. If an element has been normalized before, 
        its normalized form is stored in a cache (self.normalizing_map). When the same element needs to be 
        normalized again, the method will first check the cache and use the stored normalized form if available, 
        instead of normalizing the element again.

        The order of elements in the output will be the same as in the input. This is ensured by processing 
        the elements in the order they appear in the input and appending the normalized elements to the output 
        list in the same order.

        Args:
            element_or_elements (Union[str, List[str]]): The element or elements to normalize.

        Returns:
            str: The normalized element if the input was a string.
            list: The normalized elements if the input was a list, preserving the order of elements in the input.
        &#34;&#34;&#34;
        if isinstance(element_or_elements, str):
            denormalized_elements = [element_or_elements]
        elif isinstance(element_or_elements, list):
            denormalized_elements = element_or_elements
        else:
            raise ValueError(&#34;The element_or_elements must be either a string or a list.&#34;)
        
        normalized_elements = []
        elements_to_normalize = []
        for element in denormalized_elements:
            if element not in self.normalizing_map:
                elements_to_normalize.append(element)
        
        if elements_to_normalize:
            rendering_configs = {&#34;categories&#34;: self.normalized_elements,
                                    &#34;elements&#34;: elements_to_normalize}
            
            messages = utils.compose_initial_LLM_messages_with_templates(&#34;normalizer.applier.system.mustache&#34;, &#34;normalizer.applier.user.mustache&#34;, rendering_configs)
            next_message = openai_utils.client().send_message(messages, temperature=0.1)
            
            debug_msg = f&#34;Normalization result message: {next_message}&#34;
            logger.debug(debug_msg)
            if self.verbose:
                print(debug_msg)
    
            normalized_elements_from_llm = utils.extract_json(next_message[&#34;content&#34;])
            assert isinstance(normalized_elements_from_llm, list), &#34;The normalized element must be a list.&#34;
            assert len(normalized_elements_from_llm) == len(elements_to_normalize), &#34;The number of normalized elements must be equal to the number of elements to normalize.&#34;
    
            for i, element in enumerate(elements_to_normalize):
                normalized_element = normalized_elements_from_llm[i]
                self.normalizing_map[element] = normalized_element
        
        for element in denormalized_elements:
            normalized_elements.append(self.normalizing_map[element])
        
        return normalized_elements</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.extraction.Normalizer.normalize"><code class="name flex">
<span>def <span class="ident">normalize</span></span>(<span>self, element_or_elements: Union[str, List[str]]) ‑> Union[str, List[str]]</span>
</code></dt>
<dd>
<div class="desc"><p>Normalizes the specified element or elements.</p>
<p>This method uses a caching mechanism to improve performance. If an element has been normalized before,
its normalized form is stored in a cache (self.normalizing_map). When the same element needs to be
normalized again, the method will first check the cache and use the stored normalized form if available,
instead of normalizing the element again.</p>
<p>The order of elements in the output will be the same as in the input. This is ensured by processing
the elements in the order they appear in the input and appending the normalized elements to the output
list in the same order.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>element_or_elements</code></strong> :&ensp;<code>Union[str, List[str]]</code></dt>
<dd>The element or elements to normalize.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>str</code></dt>
<dd>The normalized element if the input was a string.</dd>
<dt><code>list</code></dt>
<dd>The normalized elements if the input was a list, preserving the order of elements in the input.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def normalize(self, element_or_elements:Union[str, List[str]]) -&gt; Union[str, List[str]]:
    &#34;&#34;&#34;
    Normalizes the specified element or elements.

    This method uses a caching mechanism to improve performance. If an element has been normalized before, 
    its normalized form is stored in a cache (self.normalizing_map). When the same element needs to be 
    normalized again, the method will first check the cache and use the stored normalized form if available, 
    instead of normalizing the element again.

    The order of elements in the output will be the same as in the input. This is ensured by processing 
    the elements in the order they appear in the input and appending the normalized elements to the output 
    list in the same order.

    Args:
        element_or_elements (Union[str, List[str]]): The element or elements to normalize.

    Returns:
        str: The normalized element if the input was a string.
        list: The normalized elements if the input was a list, preserving the order of elements in the input.
    &#34;&#34;&#34;
    if isinstance(element_or_elements, str):
        denormalized_elements = [element_or_elements]
    elif isinstance(element_or_elements, list):
        denormalized_elements = element_or_elements
    else:
        raise ValueError(&#34;The element_or_elements must be either a string or a list.&#34;)
    
    normalized_elements = []
    elements_to_normalize = []
    for element in denormalized_elements:
        if element not in self.normalizing_map:
            elements_to_normalize.append(element)
    
    if elements_to_normalize:
        rendering_configs = {&#34;categories&#34;: self.normalized_elements,
                                &#34;elements&#34;: elements_to_normalize}
        
        messages = utils.compose_initial_LLM_messages_with_templates(&#34;normalizer.applier.system.mustache&#34;, &#34;normalizer.applier.user.mustache&#34;, rendering_configs)
        next_message = openai_utils.client().send_message(messages, temperature=0.1)
        
        debug_msg = f&#34;Normalization result message: {next_message}&#34;
        logger.debug(debug_msg)
        if self.verbose:
            print(debug_msg)

        normalized_elements_from_llm = utils.extract_json(next_message[&#34;content&#34;])
        assert isinstance(normalized_elements_from_llm, list), &#34;The normalized element must be a list.&#34;
        assert len(normalized_elements_from_llm) == len(elements_to_normalize), &#34;The number of normalized elements must be equal to the number of elements to normalize.&#34;

        for i, element in enumerate(elements_to_normalize):
            normalized_element = normalized_elements_from_llm[i]
            self.normalizing_map[element] = normalized_element
    
    for element in denormalized_elements:
        normalized_elements.append(self.normalizing_map[element])
    
    return normalized_elements</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="tinytroupe.extraction.ResultsExtractor"><code class="flex name class">
<span>class <span class="ident">ResultsExtractor</span></span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class ResultsExtractor:

    def __init__(self):
        self._extraction_prompt_template_path = os.path.join(os.path.dirname(__file__), &#39;prompts/interaction_results_extractor.mustache&#39;)

        # we&#39;ll cache the last extraction results for each type of extraction, so that we can use them to
        # generate reports or other additional outputs.
        self.agent_extraction = {}
        self.world_extraction = {}

    def extract_results_from_agent(self, 
                        tinyperson:TinyPerson, 
                        extraction_objective:str=&#34;The main points present in the agent&#39;s interactions history.&#34;, 
                        situation:str = &#34;&#34;, 
                        fields:list=None,
                        fields_hints:dict=None,
                        verbose:bool=False):
        &#34;&#34;&#34;
        Extracts results from a TinyPerson instance.

        Args:
            tinyperson (TinyPerson): The TinyPerson instance to extract results from.
            extraction_objective (str): The extraction objective.
            situation (str): The situation to consider.
            fields (list, optional): The fields to extract. If None, the extractor will decide what names to use. 
                Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;

        messages = []

        rendering_configs = {}
        if fields is not None:
            rendering_configs[&#34;fields&#34;] = &#34;, &#34;.join(fields)
        
        if fields_hints is not None:
            rendering_configs[&#34;fields_hints&#34;] = list(fields_hints.items())
        
        messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(self._extraction_prompt_template_path).read(), 
                             rendering_configs)})


        interaction_history = tinyperson.pretty_current_interactions(max_content_length=None)

        extraction_request_prompt = \
f&#34;&#34;&#34;
## Extraction objective

{extraction_objective}

## Situation
You are considering a single agent, named {tinyperson.name}. Your objective thus refers to this agent specifically.
{situation}

## Agent Interactions History

You will consider an agent&#39;s history of interactions, which include stimuli it received as well as actions it 
performed.

{interaction_history}
&#34;&#34;&#34;
        messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: extraction_request_prompt})

        next_message = openai_utils.client().send_message(messages, temperature=0.0)
        
        debug_msg = f&#34;Extraction raw result message: {next_message}&#34;
        logger.debug(debug_msg)
        if verbose:
            print(debug_msg)

        if next_message is not None:
            result = utils.extract_json(next_message[&#34;content&#34;])
        else:
            result = None
        
        # cache the result
        self.agent_extraction[tinyperson.name] = result

        return result
    

    def extract_results_from_world(self, 
                                   tinyworld:TinyWorld, 
                                   extraction_objective:str=&#34;The main points that can be derived from the agents conversations and actions.&#34;, 
                                   situation:str=&#34;&#34;, 
                                   fields:list=None,
                                   fields_hints:dict=None,
                                   verbose:bool=False):
        &#34;&#34;&#34;
        Extracts results from a TinyWorld instance.

        Args:
            tinyworld (TinyWorld): The TinyWorld instance to extract results from.
            extraction_objective (str): The extraction objective.
            situation (str): The situation to consider.
            fields (list, optional): The fields to extract. If None, the extractor will decide what names to use. 
                Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;

        messages = []

        rendering_configs = {}
        if fields is not None:
            rendering_configs[&#34;fields&#34;] = &#34;, &#34;.join(fields)
        
        if fields_hints is not None:
            rendering_configs[&#34;fields_hints&#34;] = list(fields_hints.items())
        
        messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(self._extraction_prompt_template_path).read(), 
                             rendering_configs)})

        # TODO: either summarize first or break up into multiple tasks
        interaction_history = tinyworld.pretty_current_interactions(max_content_length=None)

        extraction_request_prompt = \
f&#34;&#34;&#34;
## Extraction objective

{extraction_objective}

## Situation
You are considering various agents.
{situation}

## Agents Interactions History

You will consider the history of interactions from various agents that exist in an environment called {tinyworld.name}. 
Each interaction history includes stimuli the corresponding agent received as well as actions it performed.

{interaction_history}
&#34;&#34;&#34;
        messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: extraction_request_prompt})

        next_message = openai_utils.client().send_message(messages, temperature=0.0)
        
        debug_msg = f&#34;Extraction raw result message: {next_message}&#34;
        logger.debug(debug_msg)
        if verbose:
            print(debug_msg)

        if next_message is not None:
            result = utils.extract_json(next_message[&#34;content&#34;])
        else:
            result = None
        
        # cache the result
        self.world_extraction[tinyworld.name] = result

        return result
    
    def save_as_json(self, filename:str, verbose:bool=False):
        &#34;&#34;&#34;
        Saves the last extraction results as JSON.

        Args:
            filename (str): The filename to save the JSON to.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;
        with open(filename, &#39;w&#39;) as f:
            json.dump({&#34;agent_extractions&#34;: self.agent_extraction, 
                       &#34;world_extraction&#34;: self.world_extraction}, f, indent=4)
        
        if verbose:
            print(f&#34;Saved extraction results to {filename}&#34;)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.extraction.ResultsExtractor.extract_results_from_agent"><code class="name flex">
<span>def <span class="ident">extract_results_from_agent</span></span>(<span>self, tinyperson: <a title="tinytroupe.agent.TinyPerson" href="agent.html#tinytroupe.agent.TinyPerson">TinyPerson</a>, extraction_objective: str = "The main points present in the agent's interactions history.", situation: str = '', fields: list = None, fields_hints: dict = None, verbose: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Extracts results from a TinyPerson instance.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>tinyperson</code></strong> :&ensp;<code>TinyPerson</code></dt>
<dd>The TinyPerson instance to extract results from.</dd>
<dt><strong><code>extraction_objective</code></strong> :&ensp;<code>str</code></dt>
<dd>The extraction objective.</dd>
<dt><strong><code>situation</code></strong> :&ensp;<code>str</code></dt>
<dd>The situation to consider.</dd>
<dt><strong><code>fields</code></strong> :&ensp;<code>list</code>, optional</dt>
<dd>The fields to extract. If None, the extractor will decide what names to use.
Defaults to None.</dd>
<dt><strong><code>verbose</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to print debug messages. Defaults to False.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">    def extract_results_from_agent(self, 
                        tinyperson:TinyPerson, 
                        extraction_objective:str=&#34;The main points present in the agent&#39;s interactions history.&#34;, 
                        situation:str = &#34;&#34;, 
                        fields:list=None,
                        fields_hints:dict=None,
                        verbose:bool=False):
        &#34;&#34;&#34;
        Extracts results from a TinyPerson instance.

        Args:
            tinyperson (TinyPerson): The TinyPerson instance to extract results from.
            extraction_objective (str): The extraction objective.
            situation (str): The situation to consider.
            fields (list, optional): The fields to extract. If None, the extractor will decide what names to use. 
                Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;

        messages = []

        rendering_configs = {}
        if fields is not None:
            rendering_configs[&#34;fields&#34;] = &#34;, &#34;.join(fields)
        
        if fields_hints is not None:
            rendering_configs[&#34;fields_hints&#34;] = list(fields_hints.items())
        
        messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(self._extraction_prompt_template_path).read(), 
                             rendering_configs)})


        interaction_history = tinyperson.pretty_current_interactions(max_content_length=None)

        extraction_request_prompt = \
f&#34;&#34;&#34;
## Extraction objective

{extraction_objective}

## Situation
You are considering a single agent, named {tinyperson.name}. Your objective thus refers to this agent specifically.
{situation}

## Agent Interactions History

You will consider an agent&#39;s history of interactions, which include stimuli it received as well as actions it 
performed.

{interaction_history}
&#34;&#34;&#34;
        messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: extraction_request_prompt})

        next_message = openai_utils.client().send_message(messages, temperature=0.0)
        
        debug_msg = f&#34;Extraction raw result message: {next_message}&#34;
        logger.debug(debug_msg)
        if verbose:
            print(debug_msg)

        if next_message is not None:
            result = utils.extract_json(next_message[&#34;content&#34;])
        else:
            result = None
        
        # cache the result
        self.agent_extraction[tinyperson.name] = result

        return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.extraction.ResultsExtractor.extract_results_from_world"><code class="name flex">
<span>def <span class="ident">extract_results_from_world</span></span>(<span>self, tinyworld: <a title="tinytroupe.environment.TinyWorld" href="environment.html#tinytroupe.environment.TinyWorld">TinyWorld</a>, extraction_objective: str = 'The main points that can be derived from the agents conversations and actions.', situation: str = '', fields: list = None, fields_hints: dict = None, verbose: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Extracts results from a TinyWorld instance.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>tinyworld</code></strong> :&ensp;<code>TinyWorld</code></dt>
<dd>The TinyWorld instance to extract results from.</dd>
<dt><strong><code>extraction_objective</code></strong> :&ensp;<code>str</code></dt>
<dd>The extraction objective.</dd>
<dt><strong><code>situation</code></strong> :&ensp;<code>str</code></dt>
<dd>The situation to consider.</dd>
<dt><strong><code>fields</code></strong> :&ensp;<code>list</code>, optional</dt>
<dd>The fields to extract. If None, the extractor will decide what names to use.
Defaults to None.</dd>
<dt><strong><code>verbose</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to print debug messages. Defaults to False.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">    def extract_results_from_world(self, 
                                   tinyworld:TinyWorld, 
                                   extraction_objective:str=&#34;The main points that can be derived from the agents conversations and actions.&#34;, 
                                   situation:str=&#34;&#34;, 
                                   fields:list=None,
                                   fields_hints:dict=None,
                                   verbose:bool=False):
        &#34;&#34;&#34;
        Extracts results from a TinyWorld instance.

        Args:
            tinyworld (TinyWorld): The TinyWorld instance to extract results from.
            extraction_objective (str): The extraction objective.
            situation (str): The situation to consider.
            fields (list, optional): The fields to extract. If None, the extractor will decide what names to use. 
                Defaults to None.
            verbose (bool, optional): Whether to print debug messages. Defaults to False.
        &#34;&#34;&#34;

        messages = []

        rendering_configs = {}
        if fields is not None:
            rendering_configs[&#34;fields&#34;] = &#34;, &#34;.join(fields)
        
        if fields_hints is not None:
            rendering_configs[&#34;fields_hints&#34;] = list(fields_hints.items())
        
        messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(self._extraction_prompt_template_path).read(), 
                             rendering_configs)})

        # TODO: either summarize first or break up into multiple tasks
        interaction_history = tinyworld.pretty_current_interactions(max_content_length=None)

        extraction_request_prompt = \
f&#34;&#34;&#34;
## Extraction objective

{extraction_objective}

## Situation
You are considering various agents.
{situation}

## Agents Interactions History

You will consider the history of interactions from various agents that exist in an environment called {tinyworld.name}. 
Each interaction history includes stimuli the corresponding agent received as well as actions it performed.

{interaction_history}
&#34;&#34;&#34;
        messages.append({&#34;role&#34;: &#34;user&#34;, &#34;content&#34;: extraction_request_prompt})

        next_message = openai_utils.client().send_message(messages, temperature=0.0)
        
        debug_msg = f&#34;Extraction raw result message: {next_message}&#34;
        logger.debug(debug_msg)
        if verbose:
            print(debug_msg)

        if next_message is not None:
            result = utils.extract_json(next_message[&#34;content&#34;])
        else:
            result = None
        
        # cache the result
        self.world_extraction[tinyworld.name] = result

        return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.extraction.ResultsExtractor.save_as_json"><code class="name flex">
<span>def <span class="ident">save_as_json</span></span>(<span>self, filename: str, verbose: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Saves the last extraction results as JSON.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>filename</code></strong> :&ensp;<code>str</code></dt>
<dd>The filename to save the JSON to.</dd>
<dt><strong><code>verbose</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to print debug messages. Defaults to False.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def save_as_json(self, filename:str, verbose:bool=False):
    &#34;&#34;&#34;
    Saves the last extraction results as JSON.

    Args:
        filename (str): The filename to save the JSON to.
        verbose (bool, optional): Whether to print debug messages. Defaults to False.
    &#34;&#34;&#34;
    with open(filename, &#39;w&#39;) as f:
        json.dump({&#34;agent_extractions&#34;: self.agent_extraction, 
                   &#34;world_extraction&#34;: self.world_extraction}, f, indent=4)
    
    if verbose:
        print(f&#34;Saved extraction results to {filename}&#34;)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="tinytroupe.extraction.ResultsReducer"><code class="flex name class">
<span>class <span class="ident">ResultsReducer</span></span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class ResultsReducer:

    def __init__(self):
        self.results = {}

        self.rules = {}
    
    def add_reduction_rule(self, trigger: str, func: callable):
        if trigger in self.rules:
            raise Exception(f&#34;Rule for {trigger} already exists.&#34;)
        
        self.rules[trigger] = func
    
    def reduce_agent(self, agent: TinyPerson) -&gt; list:
        reduction = []
        for message in agent.episodic_memory.retrieve_all():
            if message[&#39;role&#39;] == &#39;system&#39;:
                continue # doing nothing for `system` role yet at least

            elif message[&#39;role&#39;] == &#39;user&#39;:
                # User role is related to stimuli only
                stimulus_type = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;type&#39;]
                stimulus_content = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;content&#39;]
                stimulus_source = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;source&#39;]
                stimulus_timestamp = message[&#39;simulation_timestamp&#39;]

                if stimulus_type in self.rules:
                    extracted = self.rules[stimulus_type](focus_agent=agent, source_agent=TinyPerson.get_agent_by_name(stimulus_source), target_agent=agent, kind=&#39;stimulus&#39;, event=stimulus_type, content=stimulus_content, timestamp=stimulus_timestamp)
                    if extracted is not None:
                        reduction.append(extracted)

            elif message[&#39;role&#39;] == &#39;assistant&#39;:
                # Assistant role is related to actions only
                if &#39;action&#39; in message[&#39;content&#39;]: 
                    action_type = message[&#39;content&#39;][&#39;action&#39;][&#39;type&#39;]
                    action_content = message[&#39;content&#39;][&#39;action&#39;][&#39;content&#39;]
                    action_target = message[&#39;content&#39;][&#39;action&#39;][&#39;target&#39;]
                    action_timestamp = message[&#39;simulation_timestamp&#39;]
                    
                    if action_type in self.rules:
                        extracted = self.rules[action_type](focus_agent=agent, source_agent=agent, target_agent=TinyPerson.get_agent_by_name(action_target), kind=&#39;action&#39;, event=action_type, content=action_content, timestamp=action_timestamp)
                        if extracted is not None:
                            reduction.append(extracted)
            
        return reduction

    def reduce_agent_to_dataframe(self, agent: TinyPerson, column_names: list=None) -&gt; pd.DataFrame:
        reduction = self.reduce_agent(agent)
        return pd.DataFrame(reduction, columns=column_names)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.extraction.ResultsReducer.add_reduction_rule"><code class="name flex">
<span>def <span class="ident">add_reduction_rule</span></span>(<span>self, trigger: str, func: <built-in function callable>)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_reduction_rule(self, trigger: str, func: callable):
    if trigger in self.rules:
        raise Exception(f&#34;Rule for {trigger} already exists.&#34;)
    
    self.rules[trigger] = func</code></pre>
</details>
</dd>
<dt id="tinytroupe.extraction.ResultsReducer.reduce_agent"><code class="name flex">
<span>def <span class="ident">reduce_agent</span></span>(<span>self, agent: <a title="tinytroupe.agent.TinyPerson" href="agent.html#tinytroupe.agent.TinyPerson">TinyPerson</a>) ‑> list</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def reduce_agent(self, agent: TinyPerson) -&gt; list:
    reduction = []
    for message in agent.episodic_memory.retrieve_all():
        if message[&#39;role&#39;] == &#39;system&#39;:
            continue # doing nothing for `system` role yet at least

        elif message[&#39;role&#39;] == &#39;user&#39;:
            # User role is related to stimuli only
            stimulus_type = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;type&#39;]
            stimulus_content = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;content&#39;]
            stimulus_source = message[&#39;content&#39;][&#39;stimuli&#39;][0][&#39;source&#39;]
            stimulus_timestamp = message[&#39;simulation_timestamp&#39;]

            if stimulus_type in self.rules:
                extracted = self.rules[stimulus_type](focus_agent=agent, source_agent=TinyPerson.get_agent_by_name(stimulus_source), target_agent=agent, kind=&#39;stimulus&#39;, event=stimulus_type, content=stimulus_content, timestamp=stimulus_timestamp)
                if extracted is not None:
                    reduction.append(extracted)

        elif message[&#39;role&#39;] == &#39;assistant&#39;:
            # Assistant role is related to actions only
            if &#39;action&#39; in message[&#39;content&#39;]: 
                action_type = message[&#39;content&#39;][&#39;action&#39;][&#39;type&#39;]
                action_content = message[&#39;content&#39;][&#39;action&#39;][&#39;content&#39;]
                action_target = message[&#39;content&#39;][&#39;action&#39;][&#39;target&#39;]
                action_timestamp = message[&#39;simulation_timestamp&#39;]
                
                if action_type in self.rules:
                    extracted = self.rules[action_type](focus_agent=agent, source_agent=agent, target_agent=TinyPerson.get_agent_by_name(action_target), kind=&#39;action&#39;, event=action_type, content=action_content, timestamp=action_timestamp)
                    if extracted is not None:
                        reduction.append(extracted)
        
    return reduction</code></pre>
</details>
</dd>
<dt id="tinytroupe.extraction.ResultsReducer.reduce_agent_to_dataframe"><code class="name flex">
<span>def <span class="ident">reduce_agent_to_dataframe</span></span>(<span>self, agent: <a title="tinytroupe.agent.TinyPerson" href="agent.html#tinytroupe.agent.TinyPerson">TinyPerson</a>, column_names: list = None) ‑> pandas.core.frame.DataFrame</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def reduce_agent_to_dataframe(self, agent: TinyPerson, column_names: list=None) -&gt; pd.DataFrame:
    reduction = self.reduce_agent(agent)
    return pd.DataFrame(reduction, columns=column_names)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="tinytroupe" href="index.html">tinytroupe</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="tinytroupe.extraction.ArtifactExporter" href="#tinytroupe.extraction.ArtifactExporter">ArtifactExporter</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.extraction.ArtifactExporter.export" href="#tinytroupe.extraction.ArtifactExporter.export">export</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.extraction.Normalizer" href="#tinytroupe.extraction.Normalizer">Normalizer</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.extraction.Normalizer.normalize" href="#tinytroupe.extraction.Normalizer.normalize">normalize</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.extraction.ResultsExtractor" href="#tinytroupe.extraction.ResultsExtractor">ResultsExtractor</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.extraction.ResultsExtractor.extract_results_from_agent" href="#tinytroupe.extraction.ResultsExtractor.extract_results_from_agent">extract_results_from_agent</a></code></li>
<li><code><a title="tinytroupe.extraction.ResultsExtractor.extract_results_from_world" href="#tinytroupe.extraction.ResultsExtractor.extract_results_from_world">extract_results_from_world</a></code></li>
<li><code><a title="tinytroupe.extraction.ResultsExtractor.save_as_json" href="#tinytroupe.extraction.ResultsExtractor.save_as_json">save_as_json</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.extraction.ResultsReducer" href="#tinytroupe.extraction.ResultsReducer">ResultsReducer</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.extraction.ResultsReducer.add_reduction_rule" href="#tinytroupe.extraction.ResultsReducer.add_reduction_rule">add_reduction_rule</a></code></li>
<li><code><a title="tinytroupe.extraction.ResultsReducer.reduce_agent" href="#tinytroupe.extraction.ResultsReducer.reduce_agent">reduce_agent</a></code></li>
<li><code><a title="tinytroupe.extraction.ResultsReducer.reduce_agent_to_dataframe" href="#tinytroupe.extraction.ResultsReducer.reduce_agent_to_dataframe">reduce_agent_to_dataframe</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>