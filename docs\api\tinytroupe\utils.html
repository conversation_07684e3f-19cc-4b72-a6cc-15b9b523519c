<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>tinytroupe.utils API documentation</title>
<meta name="description" content="General utilities and convenience functions." />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>tinytroupe.utils</code></h1>
</header>
<section id="section-intro">
<p>General utilities and convenience functions.</p>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">&#34;&#34;&#34;
General utilities and convenience functions.
&#34;&#34;&#34;
import re
import json
import os
import sys
import hashlib
import textwrap
import logging
import chevron
import copy
from typing import Collection
from datetime import datetime
from pathlib import Path
import configparser
from typing import Any, TypeVar, Union
AgentOrWorld = Union[&#34;TinyPerson&#34;, &#34;TinyWorld&#34;]

# logger
logger = logging.getLogger(&#34;tinytroupe&#34;)


################################################################################
# Model input utilities
################################################################################

def compose_initial_LLM_messages_with_templates(system_template_name:str, user_template_name:str=None, rendering_configs:dict={}) -&gt; list:
    &#34;&#34;&#34;
    Composes the initial messages for the LLM model call, under the assumption that it always involves 
    a system (overall task description) and an optional user message (specific task description). 
    These messages are composed using the specified templates and rendering configurations.
    &#34;&#34;&#34;

    system_prompt_template_path = os.path.join(os.path.dirname(__file__), f&#39;prompts/{system_template_name}&#39;)
    user_prompt_template_path = os.path.join(os.path.dirname(__file__), f&#39;prompts/{user_template_name}&#39;)

    messages = []

    messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(system_prompt_template_path).read(), 
                             rendering_configs)})
    
    # optionally add a user message
    if user_template_name is not None:
        messages.append({&#34;role&#34;: &#34;user&#34;, 
                            &#34;content&#34;: chevron.render(
                                    open(user_prompt_template_path).read(), 
                                    rendering_configs)})
    return messages


################################################################################        
# Model output utilities
################################################################################
def extract_json(text: str) -&gt; dict:
    &#34;&#34;&#34;
    Extracts a JSON object from a string, ignoring: any text before the first 
    opening curly brace; and any Markdown opening (```json) or closing(```) tags.
    &#34;&#34;&#34;
    try:
        # remove any text before the first opening curly or square braces, using regex. Leave the braces.
        text = re.sub(r&#39;^.*?({|\[)&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)

        # remove any trailing text after the LAST closing curly or square braces, using regex. Leave the braces.
        text  =  re.sub(r&#39;(}|\])(?!.*(\]|\})).*$&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)
        
        # remove invalid escape sequences, which show up sometimes
        # replace \&#39; with just &#39;
        text =  re.sub(&#34;\\&#39;&#34;, &#34;&#39;&#34;, text) #re.sub(r&#39;\\\&#39;&#39;, r&#34;&#39;&#34;, text)

        # return the parsed JSON object
        return json.loads(text)
    
    except Exception:
        return {}

def extract_code_block(text: str) -&gt; str:
    &#34;&#34;&#34;
    Extracts a code block from a string, ignoring any text before the first 
    opening triple backticks and any text after the closing triple backticks.
    &#34;&#34;&#34;
    try:
        # remove any text before the first opening triple backticks, using regex. Leave the backticks.
        text = re.sub(r&#39;^.*?(```)&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)

        # remove any trailing text after the LAST closing triple backticks, using regex. Leave the backticks.
        text  =  re.sub(r&#39;(```)(?!.*```).*$&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)
        
        return text
    
    except Exception:
        return &#34;&#34;

################################################################################
# Model control utilities
################################################################################    

def repeat_on_error(retries:int, exceptions:list):
    &#34;&#34;&#34;
    Decorator that repeats the specified function call if an exception among those specified occurs, 
    up to the specified number of retries. If that number of retries is exceeded, the
    exception is raised. If no exception occurs, the function returns normally.

    Args:
        retries (int): The number of retries to attempt.
        exceptions (list): The list of exception classes to catch.
    &#34;&#34;&#34;
    def decorator(func):
        def wrapper(*args, **kwargs):
            for i in range(retries):
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    logger.debug(f&#34;Exception occurred: {e}&#34;)
                    if i == retries - 1:
                        raise e
                    else:
                        logger.debug(f&#34;Retrying ({i+1}/{retries})...&#34;)
                        continue
        return wrapper
    return decorator
   

################################################################################
# Validation
################################################################################
def check_valid_fields(obj: dict, valid_fields: list) -&gt; None:
    &#34;&#34;&#34;
    Checks whether the fields in the specified dict are valid, according to the list of valid fields. If not, raises a ValueError.
    &#34;&#34;&#34;
    for key in obj:
        if key not in valid_fields:
            raise ValueError(f&#34;Invalid key {key} in dictionary. Valid keys are: {valid_fields}&#34;)

def sanitize_raw_string(value: str) -&gt; str:
    &#34;&#34;&#34;
    Sanitizes the specified string by: 
      - removing any invalid characters.
      - ensuring it is not longer than the maximum Python string length.
    
    This is for an abundance of caution with security, to avoid any potential issues with the string.
    &#34;&#34;&#34;

    # remove any invalid characters by making sure it is a valid UTF-8 string
    value = value.encode(&#34;utf-8&#34;, &#34;ignore&#34;).decode(&#34;utf-8&#34;)

    # ensure it is not longer than the maximum Python string length
    return value[:sys.maxsize]

def sanitize_dict(value: dict) -&gt; dict:
    &#34;&#34;&#34;
    Sanitizes the specified dictionary by:
      - removing any invalid characters.
      - ensuring that the dictionary is not too deeply nested.
    &#34;&#34;&#34;

    # sanitize the string representation of the dictionary
    tmp_str = sanitize_raw_string(json.dumps(value, ensure_ascii=False))

    value = json.loads(tmp_str)

    # ensure that the dictionary is not too deeply nested
    return value
    
    
################################################################################
# Prompt engineering
################################################################################
def add_rai_template_variables_if_enabled(template_variables: dict) -&gt; dict:
    &#34;&#34;&#34;
    Adds the RAI template variables to the specified dictionary, if the RAI disclaimers are enabled.
    These can be configured in the config.ini file. If enabled, the variables will then load the RAI disclaimers from the 
    appropriate files in the prompts directory. Otherwise, the variables will be set to None.

    Args:
        template_variables (dict): The dictionary of template variables to add the RAI variables to.

    Returns:
        dict: The updated dictionary of template variables.
    &#34;&#34;&#34;

    from tinytroupe import config # avoids circular import
    rai_harmful_content_prevention = config[&#34;Simulation&#34;].getboolean(
        &#34;RAI_HARMFUL_CONTENT_PREVENTION&#34;, True 
    )
    rai_copyright_infringement_prevention = config[&#34;Simulation&#34;].getboolean(
        &#34;RAI_COPYRIGHT_INFRINGEMENT_PREVENTION&#34;, True
    )

    # Harmful content
    with open(os.path.join(os.path.dirname(__file__), &#34;prompts/rai_harmful_content_prevention.md&#34;), &#34;r&#34;) as f:
        rai_harmful_content_prevention_content = f.read()

    template_variables[&#39;rai_harmful_content_prevention&#39;] = rai_harmful_content_prevention_content if rai_harmful_content_prevention else None

    # Copyright infringement
    with open(os.path.join(os.path.dirname(__file__), &#34;prompts/rai_copyright_infringement_prevention.md&#34;), &#34;r&#34;) as f:
        rai_copyright_infringement_prevention_content = f.read()

    template_variables[&#39;rai_copyright_infringement_prevention&#39;] = rai_copyright_infringement_prevention_content if rai_copyright_infringement_prevention else None

    return template_variables

################################################################################
# Rendering and markup 
################################################################################
def inject_html_css_style_prefix(html, style_prefix_attributes):
    &#34;&#34;&#34;
    Injects a style prefix to all style attributes in the given HTML string.

    For example, if you want to add a style prefix to all style attributes in the HTML string
    ``&lt;div style=&#34;color: red;&#34;&gt;Hello&lt;/div&gt;``, you can use this function as follows:
    inject_html_css_style_prefix(&#39;&lt;div style=&#34;color: red;&#34;&gt;Hello&lt;/div&gt;&#39;, &#39;font-size: 20px;&#39;)
    &#34;&#34;&#34;
    return html.replace(&#39;style=&#34;&#39;, f&#39;style=&#34;{style_prefix_attributes};&#39;)

def break_text_at_length(text: Union[str, dict], max_length: int=None) -&gt; str:
    &#34;&#34;&#34;
    Breaks the text (or JSON) at the specified length, inserting a &#34;(...)&#34; string at the break point.
    If the maximum length is `None`, the content is returned as is.
    &#34;&#34;&#34;
    if isinstance(text, dict):
        text = json.dumps(text, indent=4)

    if max_length is None or len(text) &lt;= max_length:
        return text
    else:
        return text[:max_length] + &#34; (...)&#34;

def pretty_datetime(dt: datetime) -&gt; str:
    &#34;&#34;&#34;
    Returns a pretty string representation of the specified datetime object.
    &#34;&#34;&#34;
    return dt.strftime(&#34;%Y-%m-%d %H:%M&#34;)

def dedent(text: str) -&gt; str:
    &#34;&#34;&#34;
    Dedents the specified text, removing any leading whitespace and identation.
    &#34;&#34;&#34;
    return textwrap.dedent(text).strip()

################################################################################
# IO and startup utilities
################################################################################
_config = None

def read_config_file(use_cache=True, verbose=True) -&gt; configparser.ConfigParser:
    global _config
    if use_cache and _config is not None:
        # if we have a cached config and accept that, return it
        return _config
    
    else:
        config = configparser.ConfigParser()

        # Read the default values in the module directory.
        config_file_path = Path(__file__).parent.absolute() / &#39;config.ini&#39;
        print(f&#34;Looking for default config on: {config_file_path}&#34;) if verbose else None
        if config_file_path.exists():
            config.read(config_file_path)
            _config = config
        else:
            raise ValueError(f&#34;Failed to find default config on: {config_file_path}&#34;)

        # Now, let&#39;s override any specific default value, if there&#39;s a custom .ini config. 
        # Try the directory of the current main program
        config_file_path = Path.cwd() / &#34;config.ini&#34;
        if config_file_path.exists():
            print(f&#34;Found custom config on: {config_file_path}&#34;) if verbose else None
            config.read(config_file_path) # this only overrides the values that are present in the custom config
            _config = config
            return config
        else:
            if verbose:
                print(f&#34;Failed to find custom config on: {config_file_path}&#34;) if verbose else None
                print(&#34;Will use only default values. IF THINGS FAIL, TRY CUSTOMIZING MODEL, API TYPE, etc.&#34;) if verbose else None
        
        return config

def pretty_print_config(config):
    print()
    print(&#34;=================================&#34;)
    print(&#34;Current TinyTroupe configuration &#34;)
    print(&#34;=================================&#34;)
    for section in config.sections():
        print(f&#34;[{section}]&#34;)
        for key, value in config.items(section):
            print(f&#34;{key} = {value}&#34;)
        print()

def start_logger(config: configparser.ConfigParser):
    # create logger
    logger = logging.getLogger(&#34;tinytroupe&#34;)
    log_level = config[&#39;Logging&#39;].get(&#39;LOGLEVEL&#39;, &#39;INFO&#39;).upper()
    logger.setLevel(level=log_level)

    # create console handler and set level to debug
    ch = logging.StreamHandler()
    ch.setLevel(log_level)

    # create formatter
    formatter = logging.Formatter(&#39;%(asctime)s - %(name)s - %(levelname)s - %(message)s&#39;)

    # add formatter to ch
    ch.setFormatter(formatter)

    # add ch to logger
    logger.addHandler(ch)

class JsonSerializableRegistry:
    &#34;&#34;&#34;
    A mixin class that provides JSON serialization, deserialization, and subclass registration.
    &#34;&#34;&#34;
    
    class_mapping = {}

    def to_json(self, include: list = None, suppress: list = None, file_path: str = None) -&gt; dict:
        &#34;&#34;&#34;
        Returns a JSON representation of the object.
        
        Args:
            include (list, optional): Attributes to include in the serialization.
            suppress (list, optional): Attributes to suppress from the serialization.
            file_path (str, optional): Path to a file where the JSON will be written.
        &#34;&#34;&#34;
        # Gather all serializable attributes from the class hierarchy
        serializable_attrs = set()
        suppress_attrs = set()
        for cls in self.__class__.__mro__:
            if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
                serializable_attrs.update(cls.serializable_attributes)
            if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
                suppress_attrs.update(cls.suppress_attributes_from_serialization)
        
        # Override attributes with method parameters if provided
        if include:
            serializable_attrs = set(include)
        if suppress:
            suppress_attrs.update(suppress)
        
        result = {&#34;json_serializable_class_name&#34;: self.__class__.__name__}
        for attr in serializable_attrs if serializable_attrs else self.__dict__:
            if attr not in suppress_attrs:
                value = getattr(self, attr, None)
                if isinstance(value, JsonSerializableRegistry):
                    result[attr] = value.to_json()
                elif isinstance(value, list):
                    result[attr] = [item.to_json() if isinstance(item, JsonSerializableRegistry) else copy.deepcopy(item) for item in value]
                elif isinstance(value, dict):
                    result[attr] = {k: v.to_json() if isinstance(v, JsonSerializableRegistry) else copy.deepcopy(v) for k, v in value.items()}
                else:
                    result[attr] = copy.deepcopy(value)
        
        if file_path:
            # Create directories if they do not exist
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, &#39;w&#39;) as f:
                json.dump(result, f, indent=4)
        
        return result

    @classmethod
    def from_json(cls, json_dict_or_path, suppress: list = None, post_init_params: dict = None):
        &#34;&#34;&#34;
        Loads a JSON representation of the object and creates an instance of the class.
        
        Args:
            json_dict_or_path (dict or str): The JSON dictionary representing the object or a file path to load the JSON from.
            suppress (list, optional): Attributes to suppress from being loaded.
            
        Returns:
            An instance of the class populated with the data from json_dict_or_path.
        &#34;&#34;&#34;
        if isinstance(json_dict_or_path, str):
            with open(json_dict_or_path, &#39;r&#39;) as f:
                json_dict = json.load(f)
        else:
            json_dict = json_dict_or_path
        
        subclass_name = json_dict.get(&#34;json_serializable_class_name&#34;)
        target_class = cls.class_mapping.get(subclass_name, cls)
        instance = target_class.__new__(target_class)  # Create an instance without calling __init__
        
        # Gather all serializable attributes from the class hierarchy
        serializable_attrs = set()
        custom_serialization_initializers = {}
        suppress_attrs = set(suppress) if suppress else set()
        for cls in target_class.__mro__:
            if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
                serializable_attrs.update(cls.serializable_attributes)
            if hasattr(cls, &#39;custom_serialization_initializers&#39;) and isinstance(cls.custom_serialization_initializers, dict):
                custom_serialization_initializers.update(cls.custom_serialization_initializers)
            if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
                suppress_attrs.update(cls.suppress_attributes_from_serialization)
        
        # Assign values only for serializable attributes if specified, otherwise assign everything
        for key in serializable_attrs if serializable_attrs else json_dict:
            if key in json_dict and key not in suppress_attrs:
                value = json_dict[key]
                if key in custom_serialization_initializers:
                    # Use custom initializer if provided
                    setattr(instance, key, custom_serialization_initializers[key](value))
                elif isinstance(value, dict) and &#39;json_serializable_class_name&#39; in value:
                    # Assume it&#39;s another JsonSerializableRegistry object
                    setattr(instance, key, JsonSerializableRegistry.from_json(value))
                elif isinstance(value, list):
                    # Handle collections, recursively deserialize if items are JsonSerializableRegistry objects
                    deserialized_collection = []
                    for item in value:
                        if isinstance(item, dict) and &#39;json_serializable_class_name&#39; in item:
                            deserialized_collection.append(JsonSerializableRegistry.from_json(item))
                        else:
                            deserialized_collection.append(copy.deepcopy(item))
                    setattr(instance, key, deserialized_collection)
                else:
                    setattr(instance, key, copy.deepcopy(value))
        
        # Call post-deserialization initialization if available
        if hasattr(instance, &#39;_post_deserialization_init&#39;) and callable(instance._post_deserialization_init):
            post_init_params = post_init_params if post_init_params else {}
            instance._post_deserialization_init(**post_init_params)
        
        return instance

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Register the subclass using its name as the key
        JsonSerializableRegistry.class_mapping[cls.__name__] = cls
        
        # Automatically extend serializable attributes and custom initializers from parent classes 
        if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
            for base in cls.__bases__:
                if hasattr(base, &#39;serializable_attributes&#39;) and isinstance(base.serializable_attributes, list):
                    cls.serializable_attributes = list(set(base.serializable_attributes + cls.serializable_attributes))
        
        if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
            for base in cls.__bases__:
                if hasattr(base, &#39;suppress_attributes_from_serialization&#39;) and isinstance(base.suppress_attributes_from_serialization, list):
                    cls.suppress_attributes_from_serialization = list(set(base.suppress_attributes_from_serialization + cls.suppress_attributes_from_serialization))
        
        if hasattr(cls, &#39;custom_serialization_initializers&#39;) and isinstance(cls.custom_serialization_initializers, dict):
            for base in cls.__bases__:
                if hasattr(base, &#39;custom_serialization_initializers&#39;) and isinstance(base.custom_serialization_initializers, dict):
                    base_initializers = base.custom_serialization_initializers.copy()
                    base_initializers.update(cls.custom_serialization_initializers)
                    cls.custom_serialization_initializers = base_initializers

    def _post_deserialization_init(self, **kwargs):
        # if there&#39;s a _post_init method, call it after deserialization
        if hasattr(self, &#39;_post_init&#39;):
            self._post_init(**kwargs)


def post_init(cls):
    &#34;&#34;&#34;
    Decorator to enforce a post-initialization method call in a class, if it has one.
    The method must be named `_post_init`.
    &#34;&#34;&#34;
    original_init = cls.__init__

    def new_init(self, *args, **kwargs):
        original_init(self, *args, **kwargs)
        if hasattr(self, &#39;_post_init&#39;):
            self._post_init()

    cls.__init__ = new_init
    return cls

################################################################################
# Other
################################################################################
def name_or_empty(named_entity: AgentOrWorld):
    &#34;&#34;&#34;
    Returns the name of the specified agent or environment, or an empty string if the agent is None.
    &#34;&#34;&#34;
    if named_entity is None:
        return &#34;&#34;
    else:
        return named_entity.name

def custom_hash(obj):
    &#34;&#34;&#34;
    Returns a hash for the specified object. The object is first converted
    to a string, to make it hashable. This method is deterministic,
    contrary to the built-in hash() function.
    &#34;&#34;&#34;

    return hashlib.sha256(str(obj).encode()).hexdigest()

_fresh_id_counter = 0
def fresh_id():
    &#34;&#34;&#34;
    Returns a fresh ID for a new object. This is useful for generating unique IDs for objects.
    &#34;&#34;&#34;
    global _fresh_id_counter
    _fresh_id_counter += 1
    return _fresh_id_counter</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-functions">Functions</h2>
<dl>
<dt id="tinytroupe.utils.add_rai_template_variables_if_enabled"><code class="name flex">
<span>def <span class="ident">add_rai_template_variables_if_enabled</span></span>(<span>template_variables: dict) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the RAI template variables to the specified dictionary, if the RAI disclaimers are enabled.
These can be configured in the config.ini file. If enabled, the variables will then load the RAI disclaimers from the
appropriate files in the prompts directory. Otherwise, the variables will be set to None.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>template_variables</code></strong> :&ensp;<code>dict</code></dt>
<dd>The dictionary of template variables to add the RAI variables to.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>dict</code></dt>
<dd>The updated dictionary of template variables.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_rai_template_variables_if_enabled(template_variables: dict) -&gt; dict:
    &#34;&#34;&#34;
    Adds the RAI template variables to the specified dictionary, if the RAI disclaimers are enabled.
    These can be configured in the config.ini file. If enabled, the variables will then load the RAI disclaimers from the 
    appropriate files in the prompts directory. Otherwise, the variables will be set to None.

    Args:
        template_variables (dict): The dictionary of template variables to add the RAI variables to.

    Returns:
        dict: The updated dictionary of template variables.
    &#34;&#34;&#34;

    from tinytroupe import config # avoids circular import
    rai_harmful_content_prevention = config[&#34;Simulation&#34;].getboolean(
        &#34;RAI_HARMFUL_CONTENT_PREVENTION&#34;, True 
    )
    rai_copyright_infringement_prevention = config[&#34;Simulation&#34;].getboolean(
        &#34;RAI_COPYRIGHT_INFRINGEMENT_PREVENTION&#34;, True
    )

    # Harmful content
    with open(os.path.join(os.path.dirname(__file__), &#34;prompts/rai_harmful_content_prevention.md&#34;), &#34;r&#34;) as f:
        rai_harmful_content_prevention_content = f.read()

    template_variables[&#39;rai_harmful_content_prevention&#39;] = rai_harmful_content_prevention_content if rai_harmful_content_prevention else None

    # Copyright infringement
    with open(os.path.join(os.path.dirname(__file__), &#34;prompts/rai_copyright_infringement_prevention.md&#34;), &#34;r&#34;) as f:
        rai_copyright_infringement_prevention_content = f.read()

    template_variables[&#39;rai_copyright_infringement_prevention&#39;] = rai_copyright_infringement_prevention_content if rai_copyright_infringement_prevention else None

    return template_variables</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.break_text_at_length"><code class="name flex">
<span>def <span class="ident">break_text_at_length</span></span>(<span>text: Union[dict, str], max_length: int = None) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Breaks the text (or JSON) at the specified length, inserting a "(&hellip;)" string at the break point.
If the maximum length is <code>None</code>, the content is returned as is.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def break_text_at_length(text: Union[str, dict], max_length: int=None) -&gt; str:
    &#34;&#34;&#34;
    Breaks the text (or JSON) at the specified length, inserting a &#34;(...)&#34; string at the break point.
    If the maximum length is `None`, the content is returned as is.
    &#34;&#34;&#34;
    if isinstance(text, dict):
        text = json.dumps(text, indent=4)

    if max_length is None or len(text) &lt;= max_length:
        return text
    else:
        return text[:max_length] + &#34; (...)&#34;</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.check_valid_fields"><code class="name flex">
<span>def <span class="ident">check_valid_fields</span></span>(<span>obj: dict, valid_fields: list) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Checks whether the fields in the specified dict are valid, according to the list of valid fields. If not, raises a ValueError.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def check_valid_fields(obj: dict, valid_fields: list) -&gt; None:
    &#34;&#34;&#34;
    Checks whether the fields in the specified dict are valid, according to the list of valid fields. If not, raises a ValueError.
    &#34;&#34;&#34;
    for key in obj:
        if key not in valid_fields:
            raise ValueError(f&#34;Invalid key {key} in dictionary. Valid keys are: {valid_fields}&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.compose_initial_LLM_messages_with_templates"><code class="name flex">
<span>def <span class="ident">compose_initial_LLM_messages_with_templates</span></span>(<span>system_template_name: str, user_template_name: str = None, rendering_configs: dict = {}) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Composes the initial messages for the LLM model call, under the assumption that it always involves
a system (overall task description) and an optional user message (specific task description).
These messages are composed using the specified templates and rendering configurations.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def compose_initial_LLM_messages_with_templates(system_template_name:str, user_template_name:str=None, rendering_configs:dict={}) -&gt; list:
    &#34;&#34;&#34;
    Composes the initial messages for the LLM model call, under the assumption that it always involves 
    a system (overall task description) and an optional user message (specific task description). 
    These messages are composed using the specified templates and rendering configurations.
    &#34;&#34;&#34;

    system_prompt_template_path = os.path.join(os.path.dirname(__file__), f&#39;prompts/{system_template_name}&#39;)
    user_prompt_template_path = os.path.join(os.path.dirname(__file__), f&#39;prompts/{user_template_name}&#39;)

    messages = []

    messages.append({&#34;role&#34;: &#34;system&#34;, 
                         &#34;content&#34;: chevron.render(
                             open(system_prompt_template_path).read(), 
                             rendering_configs)})
    
    # optionally add a user message
    if user_template_name is not None:
        messages.append({&#34;role&#34;: &#34;user&#34;, 
                            &#34;content&#34;: chevron.render(
                                    open(user_prompt_template_path).read(), 
                                    rendering_configs)})
    return messages</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.custom_hash"><code class="name flex">
<span>def <span class="ident">custom_hash</span></span>(<span>obj)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a hash for the specified object. The object is first converted
to a string, to make it hashable. This method is deterministic,
contrary to the built-in hash() function.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def custom_hash(obj):
    &#34;&#34;&#34;
    Returns a hash for the specified object. The object is first converted
    to a string, to make it hashable. This method is deterministic,
    contrary to the built-in hash() function.
    &#34;&#34;&#34;

    return hashlib.sha256(str(obj).encode()).hexdigest()</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.dedent"><code class="name flex">
<span>def <span class="ident">dedent</span></span>(<span>text: str) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Dedents the specified text, removing any leading whitespace and identation.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def dedent(text: str) -&gt; str:
    &#34;&#34;&#34;
    Dedents the specified text, removing any leading whitespace and identation.
    &#34;&#34;&#34;
    return textwrap.dedent(text).strip()</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.extract_code_block"><code class="name flex">
<span>def <span class="ident">extract_code_block</span></span>(<span>text: str) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Extracts a code block from a string, ignoring any text before the first
opening triple backticks and any text after the closing triple backticks.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def extract_code_block(text: str) -&gt; str:
    &#34;&#34;&#34;
    Extracts a code block from a string, ignoring any text before the first 
    opening triple backticks and any text after the closing triple backticks.
    &#34;&#34;&#34;
    try:
        # remove any text before the first opening triple backticks, using regex. Leave the backticks.
        text = re.sub(r&#39;^.*?(```)&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)

        # remove any trailing text after the LAST closing triple backticks, using regex. Leave the backticks.
        text  =  re.sub(r&#39;(```)(?!.*```).*$&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)
        
        return text
    
    except Exception:
        return &#34;&#34;</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.extract_json"><code class="name flex">
<span>def <span class="ident">extract_json</span></span>(<span>text: str) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Extracts a JSON object from a string, ignoring: any text before the first
opening curly brace; and any Markdown opening (<code>json) or closing(</code>) tags.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def extract_json(text: str) -&gt; dict:
    &#34;&#34;&#34;
    Extracts a JSON object from a string, ignoring: any text before the first 
    opening curly brace; and any Markdown opening (```json) or closing(```) tags.
    &#34;&#34;&#34;
    try:
        # remove any text before the first opening curly or square braces, using regex. Leave the braces.
        text = re.sub(r&#39;^.*?({|\[)&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)

        # remove any trailing text after the LAST closing curly or square braces, using regex. Leave the braces.
        text  =  re.sub(r&#39;(}|\])(?!.*(\]|\})).*$&#39;, r&#39;\1&#39;, text, flags=re.DOTALL)
        
        # remove invalid escape sequences, which show up sometimes
        # replace \&#39; with just &#39;
        text =  re.sub(&#34;\\&#39;&#34;, &#34;&#39;&#34;, text) #re.sub(r&#39;\\\&#39;&#39;, r&#34;&#39;&#34;, text)

        # return the parsed JSON object
        return json.loads(text)
    
    except Exception:
        return {}</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.fresh_id"><code class="name flex">
<span>def <span class="ident">fresh_id</span></span>(<span>)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a fresh ID for a new object. This is useful for generating unique IDs for objects.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def fresh_id():
    &#34;&#34;&#34;
    Returns a fresh ID for a new object. This is useful for generating unique IDs for objects.
    &#34;&#34;&#34;
    global _fresh_id_counter
    _fresh_id_counter += 1
    return _fresh_id_counter</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.inject_html_css_style_prefix"><code class="name flex">
<span>def <span class="ident">inject_html_css_style_prefix</span></span>(<span>html, style_prefix_attributes)</span>
</code></dt>
<dd>
<div class="desc"><p>Injects a style prefix to all style attributes in the given HTML string.</p>
<p>For example, if you want to add a style prefix to all style attributes in the HTML string
<code>&lt;div style="color: red;"&gt;Hello&lt;/div&gt;</code>, you can use this function as follows:
inject_html_css_style_prefix('<div style="color: red;">Hello</div>', 'font-size: 20px;')</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def inject_html_css_style_prefix(html, style_prefix_attributes):
    &#34;&#34;&#34;
    Injects a style prefix to all style attributes in the given HTML string.

    For example, if you want to add a style prefix to all style attributes in the HTML string
    ``&lt;div style=&#34;color: red;&#34;&gt;Hello&lt;/div&gt;``, you can use this function as follows:
    inject_html_css_style_prefix(&#39;&lt;div style=&#34;color: red;&#34;&gt;Hello&lt;/div&gt;&#39;, &#39;font-size: 20px;&#39;)
    &#34;&#34;&#34;
    return html.replace(&#39;style=&#34;&#39;, f&#39;style=&#34;{style_prefix_attributes};&#39;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.name_or_empty"><code class="name flex">
<span>def <span class="ident">name_or_empty</span></span>(<span>named_entity: Union[ForwardRef('TinyPerson'), ForwardRef('TinyWorld')])</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the name of the specified agent or environment, or an empty string if the agent is None.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def name_or_empty(named_entity: AgentOrWorld):
    &#34;&#34;&#34;
    Returns the name of the specified agent or environment, or an empty string if the agent is None.
    &#34;&#34;&#34;
    if named_entity is None:
        return &#34;&#34;
    else:
        return named_entity.name</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.post_init"><code class="name flex">
<span>def <span class="ident">post_init</span></span>(<span>cls)</span>
</code></dt>
<dd>
<div class="desc"><p>Decorator to enforce a post-initialization method call in a class, if it has one.
The method must be named <code>_post_init</code>.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def post_init(cls):
    &#34;&#34;&#34;
    Decorator to enforce a post-initialization method call in a class, if it has one.
    The method must be named `_post_init`.
    &#34;&#34;&#34;
    original_init = cls.__init__

    def new_init(self, *args, **kwargs):
        original_init(self, *args, **kwargs)
        if hasattr(self, &#39;_post_init&#39;):
            self._post_init()

    cls.__init__ = new_init
    return cls</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.pretty_datetime"><code class="name flex">
<span>def <span class="ident">pretty_datetime</span></span>(<span>dt: datetime.datetime) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a pretty string representation of the specified datetime object.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def pretty_datetime(dt: datetime) -&gt; str:
    &#34;&#34;&#34;
    Returns a pretty string representation of the specified datetime object.
    &#34;&#34;&#34;
    return dt.strftime(&#34;%Y-%m-%d %H:%M&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.pretty_print_config"><code class="name flex">
<span>def <span class="ident">pretty_print_config</span></span>(<span>config)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def pretty_print_config(config):
    print()
    print(&#34;=================================&#34;)
    print(&#34;Current TinyTroupe configuration &#34;)
    print(&#34;=================================&#34;)
    for section in config.sections():
        print(f&#34;[{section}]&#34;)
        for key, value in config.items(section):
            print(f&#34;{key} = {value}&#34;)
        print()</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.read_config_file"><code class="name flex">
<span>def <span class="ident">read_config_file</span></span>(<span>use_cache=True, verbose=True) ‑> configparser.ConfigParser</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def read_config_file(use_cache=True, verbose=True) -&gt; configparser.ConfigParser:
    global _config
    if use_cache and _config is not None:
        # if we have a cached config and accept that, return it
        return _config
    
    else:
        config = configparser.ConfigParser()

        # Read the default values in the module directory.
        config_file_path = Path(__file__).parent.absolute() / &#39;config.ini&#39;
        print(f&#34;Looking for default config on: {config_file_path}&#34;) if verbose else None
        if config_file_path.exists():
            config.read(config_file_path)
            _config = config
        else:
            raise ValueError(f&#34;Failed to find default config on: {config_file_path}&#34;)

        # Now, let&#39;s override any specific default value, if there&#39;s a custom .ini config. 
        # Try the directory of the current main program
        config_file_path = Path.cwd() / &#34;config.ini&#34;
        if config_file_path.exists():
            print(f&#34;Found custom config on: {config_file_path}&#34;) if verbose else None
            config.read(config_file_path) # this only overrides the values that are present in the custom config
            _config = config
            return config
        else:
            if verbose:
                print(f&#34;Failed to find custom config on: {config_file_path}&#34;) if verbose else None
                print(&#34;Will use only default values. IF THINGS FAIL, TRY CUSTOMIZING MODEL, API TYPE, etc.&#34;) if verbose else None
        
        return config</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.repeat_on_error"><code class="name flex">
<span>def <span class="ident">repeat_on_error</span></span>(<span>retries: int, exceptions: list)</span>
</code></dt>
<dd>
<div class="desc"><p>Decorator that repeats the specified function call if an exception among those specified occurs,
up to the specified number of retries. If that number of retries is exceeded, the
exception is raised. If no exception occurs, the function returns normally.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>retries</code></strong> :&ensp;<code>int</code></dt>
<dd>The number of retries to attempt.</dd>
<dt><strong><code>exceptions</code></strong> :&ensp;<code>list</code></dt>
<dd>The list of exception classes to catch.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def repeat_on_error(retries:int, exceptions:list):
    &#34;&#34;&#34;
    Decorator that repeats the specified function call if an exception among those specified occurs, 
    up to the specified number of retries. If that number of retries is exceeded, the
    exception is raised. If no exception occurs, the function returns normally.

    Args:
        retries (int): The number of retries to attempt.
        exceptions (list): The list of exception classes to catch.
    &#34;&#34;&#34;
    def decorator(func):
        def wrapper(*args, **kwargs):
            for i in range(retries):
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    logger.debug(f&#34;Exception occurred: {e}&#34;)
                    if i == retries - 1:
                        raise e
                    else:
                        logger.debug(f&#34;Retrying ({i+1}/{retries})...&#34;)
                        continue
        return wrapper
    return decorator</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.sanitize_dict"><code class="name flex">
<span>def <span class="ident">sanitize_dict</span></span>(<span>value: dict) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Sanitizes the specified dictionary by:
- removing any invalid characters.
- ensuring that the dictionary is not too deeply nested.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def sanitize_dict(value: dict) -&gt; dict:
    &#34;&#34;&#34;
    Sanitizes the specified dictionary by:
      - removing any invalid characters.
      - ensuring that the dictionary is not too deeply nested.
    &#34;&#34;&#34;

    # sanitize the string representation of the dictionary
    tmp_str = sanitize_raw_string(json.dumps(value, ensure_ascii=False))

    value = json.loads(tmp_str)

    # ensure that the dictionary is not too deeply nested
    return value</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.sanitize_raw_string"><code class="name flex">
<span>def <span class="ident">sanitize_raw_string</span></span>(<span>value: str) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Sanitizes the specified string by:
- removing any invalid characters.
- ensuring it is not longer than the maximum Python string length.</p>
<p>This is for an abundance of caution with security, to avoid any potential issues with the string.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def sanitize_raw_string(value: str) -&gt; str:
    &#34;&#34;&#34;
    Sanitizes the specified string by: 
      - removing any invalid characters.
      - ensuring it is not longer than the maximum Python string length.
    
    This is for an abundance of caution with security, to avoid any potential issues with the string.
    &#34;&#34;&#34;

    # remove any invalid characters by making sure it is a valid UTF-8 string
    value = value.encode(&#34;utf-8&#34;, &#34;ignore&#34;).decode(&#34;utf-8&#34;)

    # ensure it is not longer than the maximum Python string length
    return value[:sys.maxsize]</code></pre>
</details>
</dd>
<dt id="tinytroupe.utils.start_logger"><code class="name flex">
<span>def <span class="ident">start_logger</span></span>(<span>config: configparser.ConfigParser)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def start_logger(config: configparser.ConfigParser):
    # create logger
    logger = logging.getLogger(&#34;tinytroupe&#34;)
    log_level = config[&#39;Logging&#39;].get(&#39;LOGLEVEL&#39;, &#39;INFO&#39;).upper()
    logger.setLevel(level=log_level)

    # create console handler and set level to debug
    ch = logging.StreamHandler()
    ch.setLevel(log_level)

    # create formatter
    formatter = logging.Formatter(&#39;%(asctime)s - %(name)s - %(levelname)s - %(message)s&#39;)

    # add formatter to ch
    ch.setFormatter(formatter)

    # add ch to logger
    logger.addHandler(ch)</code></pre>
</details>
</dd>
</dl>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="tinytroupe.utils.JsonSerializableRegistry"><code class="flex name class">
<span>class <span class="ident">JsonSerializableRegistry</span></span>
</code></dt>
<dd>
<div class="desc"><p>A mixin class that provides JSON serialization, deserialization, and subclass registration.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class JsonSerializableRegistry:
    &#34;&#34;&#34;
    A mixin class that provides JSON serialization, deserialization, and subclass registration.
    &#34;&#34;&#34;
    
    class_mapping = {}

    def to_json(self, include: list = None, suppress: list = None, file_path: str = None) -&gt; dict:
        &#34;&#34;&#34;
        Returns a JSON representation of the object.
        
        Args:
            include (list, optional): Attributes to include in the serialization.
            suppress (list, optional): Attributes to suppress from the serialization.
            file_path (str, optional): Path to a file where the JSON will be written.
        &#34;&#34;&#34;
        # Gather all serializable attributes from the class hierarchy
        serializable_attrs = set()
        suppress_attrs = set()
        for cls in self.__class__.__mro__:
            if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
                serializable_attrs.update(cls.serializable_attributes)
            if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
                suppress_attrs.update(cls.suppress_attributes_from_serialization)
        
        # Override attributes with method parameters if provided
        if include:
            serializable_attrs = set(include)
        if suppress:
            suppress_attrs.update(suppress)
        
        result = {&#34;json_serializable_class_name&#34;: self.__class__.__name__}
        for attr in serializable_attrs if serializable_attrs else self.__dict__:
            if attr not in suppress_attrs:
                value = getattr(self, attr, None)
                if isinstance(value, JsonSerializableRegistry):
                    result[attr] = value.to_json()
                elif isinstance(value, list):
                    result[attr] = [item.to_json() if isinstance(item, JsonSerializableRegistry) else copy.deepcopy(item) for item in value]
                elif isinstance(value, dict):
                    result[attr] = {k: v.to_json() if isinstance(v, JsonSerializableRegistry) else copy.deepcopy(v) for k, v in value.items()}
                else:
                    result[attr] = copy.deepcopy(value)
        
        if file_path:
            # Create directories if they do not exist
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, &#39;w&#39;) as f:
                json.dump(result, f, indent=4)
        
        return result

    @classmethod
    def from_json(cls, json_dict_or_path, suppress: list = None, post_init_params: dict = None):
        &#34;&#34;&#34;
        Loads a JSON representation of the object and creates an instance of the class.
        
        Args:
            json_dict_or_path (dict or str): The JSON dictionary representing the object or a file path to load the JSON from.
            suppress (list, optional): Attributes to suppress from being loaded.
            
        Returns:
            An instance of the class populated with the data from json_dict_or_path.
        &#34;&#34;&#34;
        if isinstance(json_dict_or_path, str):
            with open(json_dict_or_path, &#39;r&#39;) as f:
                json_dict = json.load(f)
        else:
            json_dict = json_dict_or_path
        
        subclass_name = json_dict.get(&#34;json_serializable_class_name&#34;)
        target_class = cls.class_mapping.get(subclass_name, cls)
        instance = target_class.__new__(target_class)  # Create an instance without calling __init__
        
        # Gather all serializable attributes from the class hierarchy
        serializable_attrs = set()
        custom_serialization_initializers = {}
        suppress_attrs = set(suppress) if suppress else set()
        for cls in target_class.__mro__:
            if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
                serializable_attrs.update(cls.serializable_attributes)
            if hasattr(cls, &#39;custom_serialization_initializers&#39;) and isinstance(cls.custom_serialization_initializers, dict):
                custom_serialization_initializers.update(cls.custom_serialization_initializers)
            if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
                suppress_attrs.update(cls.suppress_attributes_from_serialization)
        
        # Assign values only for serializable attributes if specified, otherwise assign everything
        for key in serializable_attrs if serializable_attrs else json_dict:
            if key in json_dict and key not in suppress_attrs:
                value = json_dict[key]
                if key in custom_serialization_initializers:
                    # Use custom initializer if provided
                    setattr(instance, key, custom_serialization_initializers[key](value))
                elif isinstance(value, dict) and &#39;json_serializable_class_name&#39; in value:
                    # Assume it&#39;s another JsonSerializableRegistry object
                    setattr(instance, key, JsonSerializableRegistry.from_json(value))
                elif isinstance(value, list):
                    # Handle collections, recursively deserialize if items are JsonSerializableRegistry objects
                    deserialized_collection = []
                    for item in value:
                        if isinstance(item, dict) and &#39;json_serializable_class_name&#39; in item:
                            deserialized_collection.append(JsonSerializableRegistry.from_json(item))
                        else:
                            deserialized_collection.append(copy.deepcopy(item))
                    setattr(instance, key, deserialized_collection)
                else:
                    setattr(instance, key, copy.deepcopy(value))
        
        # Call post-deserialization initialization if available
        if hasattr(instance, &#39;_post_deserialization_init&#39;) and callable(instance._post_deserialization_init):
            post_init_params = post_init_params if post_init_params else {}
            instance._post_deserialization_init(**post_init_params)
        
        return instance

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Register the subclass using its name as the key
        JsonSerializableRegistry.class_mapping[cls.__name__] = cls
        
        # Automatically extend serializable attributes and custom initializers from parent classes 
        if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
            for base in cls.__bases__:
                if hasattr(base, &#39;serializable_attributes&#39;) and isinstance(base.serializable_attributes, list):
                    cls.serializable_attributes = list(set(base.serializable_attributes + cls.serializable_attributes))
        
        if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
            for base in cls.__bases__:
                if hasattr(base, &#39;suppress_attributes_from_serialization&#39;) and isinstance(base.suppress_attributes_from_serialization, list):
                    cls.suppress_attributes_from_serialization = list(set(base.suppress_attributes_from_serialization + cls.suppress_attributes_from_serialization))
        
        if hasattr(cls, &#39;custom_serialization_initializers&#39;) and isinstance(cls.custom_serialization_initializers, dict):
            for base in cls.__bases__:
                if hasattr(base, &#39;custom_serialization_initializers&#39;) and isinstance(base.custom_serialization_initializers, dict):
                    base_initializers = base.custom_serialization_initializers.copy()
                    base_initializers.update(cls.custom_serialization_initializers)
                    cls.custom_serialization_initializers = base_initializers

    def _post_deserialization_init(self, **kwargs):
        # if there&#39;s a _post_init method, call it after deserialization
        if hasattr(self, &#39;_post_init&#39;):
            self._post_init(**kwargs)</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.TinyMentalFaculty" href="agent.html#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></li>
<li><a title="tinytroupe.agent.TinyPerson" href="agent.html#tinytroupe.agent.TinyPerson">TinyPerson</a></li>
<li><a title="tinytroupe.enrichment.TinyEnricher" href="enrichment.html#tinytroupe.enrichment.TinyEnricher">TinyEnricher</a></li>
<li><a title="tinytroupe.extraction.ArtifactExporter" href="extraction.html#tinytroupe.extraction.ArtifactExporter">ArtifactExporter</a></li>
<li><a title="tinytroupe.tools.TinyTool" href="tools.html#tinytroupe.tools.TinyTool">TinyTool</a></li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="tinytroupe.utils.JsonSerializableRegistry.class_mapping"><code class="name">var <span class="ident">class_mapping</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
<h3>Static methods</h3>
<dl>
<dt id="tinytroupe.utils.JsonSerializableRegistry.from_json"><code class="name flex">
<span>def <span class="ident">from_json</span></span>(<span>json_dict_or_path, suppress: list = None, post_init_params: dict = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Loads a JSON representation of the object and creates an instance of the class.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>json_dict_or_path</code></strong> :&ensp;<code>dict</code> or <code>str</code></dt>
<dd>The JSON dictionary representing the object or a file path to load the JSON from.</dd>
<dt><strong><code>suppress</code></strong> :&ensp;<code>list</code>, optional</dt>
<dd>Attributes to suppress from being loaded.</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>An instance of the class populated with the data from json_dict_or_path.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@classmethod
def from_json(cls, json_dict_or_path, suppress: list = None, post_init_params: dict = None):
    &#34;&#34;&#34;
    Loads a JSON representation of the object and creates an instance of the class.
    
    Args:
        json_dict_or_path (dict or str): The JSON dictionary representing the object or a file path to load the JSON from.
        suppress (list, optional): Attributes to suppress from being loaded.
        
    Returns:
        An instance of the class populated with the data from json_dict_or_path.
    &#34;&#34;&#34;
    if isinstance(json_dict_or_path, str):
        with open(json_dict_or_path, &#39;r&#39;) as f:
            json_dict = json.load(f)
    else:
        json_dict = json_dict_or_path
    
    subclass_name = json_dict.get(&#34;json_serializable_class_name&#34;)
    target_class = cls.class_mapping.get(subclass_name, cls)
    instance = target_class.__new__(target_class)  # Create an instance without calling __init__
    
    # Gather all serializable attributes from the class hierarchy
    serializable_attrs = set()
    custom_serialization_initializers = {}
    suppress_attrs = set(suppress) if suppress else set()
    for cls in target_class.__mro__:
        if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
            serializable_attrs.update(cls.serializable_attributes)
        if hasattr(cls, &#39;custom_serialization_initializers&#39;) and isinstance(cls.custom_serialization_initializers, dict):
            custom_serialization_initializers.update(cls.custom_serialization_initializers)
        if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
            suppress_attrs.update(cls.suppress_attributes_from_serialization)
    
    # Assign values only for serializable attributes if specified, otherwise assign everything
    for key in serializable_attrs if serializable_attrs else json_dict:
        if key in json_dict and key not in suppress_attrs:
            value = json_dict[key]
            if key in custom_serialization_initializers:
                # Use custom initializer if provided
                setattr(instance, key, custom_serialization_initializers[key](value))
            elif isinstance(value, dict) and &#39;json_serializable_class_name&#39; in value:
                # Assume it&#39;s another JsonSerializableRegistry object
                setattr(instance, key, JsonSerializableRegistry.from_json(value))
            elif isinstance(value, list):
                # Handle collections, recursively deserialize if items are JsonSerializableRegistry objects
                deserialized_collection = []
                for item in value:
                    if isinstance(item, dict) and &#39;json_serializable_class_name&#39; in item:
                        deserialized_collection.append(JsonSerializableRegistry.from_json(item))
                    else:
                        deserialized_collection.append(copy.deepcopy(item))
                setattr(instance, key, deserialized_collection)
            else:
                setattr(instance, key, copy.deepcopy(value))
    
    # Call post-deserialization initialization if available
    if hasattr(instance, &#39;_post_deserialization_init&#39;) and callable(instance._post_deserialization_init):
        post_init_params = post_init_params if post_init_params else {}
        instance._post_deserialization_init(**post_init_params)
    
    return instance</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.utils.JsonSerializableRegistry.to_json"><code class="name flex">
<span>def <span class="ident">to_json</span></span>(<span>self, include: list = None, suppress: list = None, file_path: str = None) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a JSON representation of the object.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>include</code></strong> :&ensp;<code>list</code>, optional</dt>
<dd>Attributes to include in the serialization.</dd>
<dt><strong><code>suppress</code></strong> :&ensp;<code>list</code>, optional</dt>
<dd>Attributes to suppress from the serialization.</dd>
<dt><strong><code>file_path</code></strong> :&ensp;<code>str</code>, optional</dt>
<dd>Path to a file where the JSON will be written.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def to_json(self, include: list = None, suppress: list = None, file_path: str = None) -&gt; dict:
    &#34;&#34;&#34;
    Returns a JSON representation of the object.
    
    Args:
        include (list, optional): Attributes to include in the serialization.
        suppress (list, optional): Attributes to suppress from the serialization.
        file_path (str, optional): Path to a file where the JSON will be written.
    &#34;&#34;&#34;
    # Gather all serializable attributes from the class hierarchy
    serializable_attrs = set()
    suppress_attrs = set()
    for cls in self.__class__.__mro__:
        if hasattr(cls, &#39;serializable_attributes&#39;) and isinstance(cls.serializable_attributes, list):
            serializable_attrs.update(cls.serializable_attributes)
        if hasattr(cls, &#39;suppress_attributes_from_serialization&#39;) and isinstance(cls.suppress_attributes_from_serialization, list):
            suppress_attrs.update(cls.suppress_attributes_from_serialization)
    
    # Override attributes with method parameters if provided
    if include:
        serializable_attrs = set(include)
    if suppress:
        suppress_attrs.update(suppress)
    
    result = {&#34;json_serializable_class_name&#34;: self.__class__.__name__}
    for attr in serializable_attrs if serializable_attrs else self.__dict__:
        if attr not in suppress_attrs:
            value = getattr(self, attr, None)
            if isinstance(value, JsonSerializableRegistry):
                result[attr] = value.to_json()
            elif isinstance(value, list):
                result[attr] = [item.to_json() if isinstance(item, JsonSerializableRegistry) else copy.deepcopy(item) for item in value]
            elif isinstance(value, dict):
                result[attr] = {k: v.to_json() if isinstance(v, JsonSerializableRegistry) else copy.deepcopy(v) for k, v in value.items()}
            else:
                result[attr] = copy.deepcopy(value)
    
    if file_path:
        # Create directories if they do not exist
        import os
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, &#39;w&#39;) as f:
            json.dump(result, f, indent=4)
    
    return result</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="tinytroupe" href="index.html">tinytroupe</a></code></li>
</ul>
</li>
<li><h3><a href="#header-functions">Functions</a></h3>
<ul class="">
<li><code><a title="tinytroupe.utils.add_rai_template_variables_if_enabled" href="#tinytroupe.utils.add_rai_template_variables_if_enabled">add_rai_template_variables_if_enabled</a></code></li>
<li><code><a title="tinytroupe.utils.break_text_at_length" href="#tinytroupe.utils.break_text_at_length">break_text_at_length</a></code></li>
<li><code><a title="tinytroupe.utils.check_valid_fields" href="#tinytroupe.utils.check_valid_fields">check_valid_fields</a></code></li>
<li><code><a title="tinytroupe.utils.compose_initial_LLM_messages_with_templates" href="#tinytroupe.utils.compose_initial_LLM_messages_with_templates">compose_initial_LLM_messages_with_templates</a></code></li>
<li><code><a title="tinytroupe.utils.custom_hash" href="#tinytroupe.utils.custom_hash">custom_hash</a></code></li>
<li><code><a title="tinytroupe.utils.dedent" href="#tinytroupe.utils.dedent">dedent</a></code></li>
<li><code><a title="tinytroupe.utils.extract_code_block" href="#tinytroupe.utils.extract_code_block">extract_code_block</a></code></li>
<li><code><a title="tinytroupe.utils.extract_json" href="#tinytroupe.utils.extract_json">extract_json</a></code></li>
<li><code><a title="tinytroupe.utils.fresh_id" href="#tinytroupe.utils.fresh_id">fresh_id</a></code></li>
<li><code><a title="tinytroupe.utils.inject_html_css_style_prefix" href="#tinytroupe.utils.inject_html_css_style_prefix">inject_html_css_style_prefix</a></code></li>
<li><code><a title="tinytroupe.utils.name_or_empty" href="#tinytroupe.utils.name_or_empty">name_or_empty</a></code></li>
<li><code><a title="tinytroupe.utils.post_init" href="#tinytroupe.utils.post_init">post_init</a></code></li>
<li><code><a title="tinytroupe.utils.pretty_datetime" href="#tinytroupe.utils.pretty_datetime">pretty_datetime</a></code></li>
<li><code><a title="tinytroupe.utils.pretty_print_config" href="#tinytroupe.utils.pretty_print_config">pretty_print_config</a></code></li>
<li><code><a title="tinytroupe.utils.read_config_file" href="#tinytroupe.utils.read_config_file">read_config_file</a></code></li>
<li><code><a title="tinytroupe.utils.repeat_on_error" href="#tinytroupe.utils.repeat_on_error">repeat_on_error</a></code></li>
<li><code><a title="tinytroupe.utils.sanitize_dict" href="#tinytroupe.utils.sanitize_dict">sanitize_dict</a></code></li>
<li><code><a title="tinytroupe.utils.sanitize_raw_string" href="#tinytroupe.utils.sanitize_raw_string">sanitize_raw_string</a></code></li>
<li><code><a title="tinytroupe.utils.start_logger" href="#tinytroupe.utils.start_logger">start_logger</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="tinytroupe.utils.JsonSerializableRegistry" href="#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.class_mapping" href="#tinytroupe.utils.JsonSerializableRegistry.class_mapping">class_mapping</a></code></li>
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.from_json" href="#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.to_json" href="#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>