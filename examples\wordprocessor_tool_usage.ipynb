{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Wordprocessor usage example\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "!!!!\n", "DISCLAIMER: TinyTroupe relies on Artificial Intelligence (AI) models to generate content. \n", "The AI models are not perfect and may produce inappropriate or inacurate results. \n", "For any serious or consequential use, please review the generated content before using it.\n", "!!!!\n", "\n", "Looking for default config on: c:\\Users\\<USER>\\OneDrive - Microsoft\\Git repositories\\tinytroupe-opensource\\TinyTroupe\\examples\\..\\tinytroupe\\utils\\..\\config.ini\n", "Found custom config on: c:\\Users\\<USER>\\OneDrive - Microsoft\\Git repositories\\tinytroupe-opensource\\TinyTroupe\\examples\\config.ini\n", "\n", "=================================\n", "Current TinyTroupe configuration \n", "=================================\n", "[OpenAI]\n", "api_type = openai\n", "azure_api_version = 2024-08-01-preview\n", "model = gpt-4o-mini\n", "max_tokens = 4000\n", "temperature = 1.2\n", "freq_penalty = 0.0\n", "presence_penalty = 0.0\n", "timeout = 60\n", "max_attempts = 5\n", "waiting_time = 2\n", "exponential_backoff_factor = 5\n", "embedding_model = text-embedding-3-small\n", "cache_api_calls = False\n", "cache_file_name = openai_api_cache.pickle\n", "max_content_display_length = 1024\n", "azure_embedding_model_api_version = 2023-05-15\n", "\n", "[Simulation]\n", "rai_harmful_content_prevention = True\n", "rai_copyright_infringement_prevention = True\n", "\n", "[Logging]\n", "loglevel = ERROR\n", "\n"]}], "source": ["import json\n", "import sys\n", "import csv\n", "sys.path.insert(0, '..') # ensures that the package is imported from the parent directory, not the Python installation\n", "\n", "\n", "import tinytroupe\n", "from tinytroupe.openai_utils import force_api_type\n", "from tinytroupe.factory import TinyPersonFactory\n", "from tinytroupe.agent import <PERSON><PERSON><PERSON>, TinyToolUse\n", "from tinytroupe.environment import TinyWorld\n", "from tinytroupe import control\n", "from tinytroupe.extraction import ResultsExtractor, ResultsReducer\n", "from tinytroupe.enrichment import TinyEnricher\n", "from tinytroupe.extraction import ArtifactExporter\n", "from tinytroupe.tools import TinyWordProcessor\n", "from tinytroupe.steering import TinyStory\n", "import tinytroupe.utils as utils\n", "from tinytroupe.examples import create_lisa_the_data_scientist, create_oscar_the_architect, create_marcos_the_physician"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["data_export_folder = \"../data/extractions/wordprocessor\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["exporter = ArtifactExporter(base_output_folder=data_export_folder)\n", "enricher = TinyEnricher()\n", "tooluse_faculty = TinyToolUse(tools=[TinyWordProcessor(exporter=exporter, enricher=enricher)])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["lisa = create_lisa_the_data_scientist()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON><PERSON><PERSON>(name='<PERSON>')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["lisa.add_mental_faculties([tooluse_faculty])\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic; text-decoration: underline\">USER</span><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic\"> --&gt; </span><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic; text-decoration: underline\"><PERSON></span><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic\">: [CONVERSATION] </span>\n", "<span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic\">          &gt; You have just been fired and need to find a new job. You decide to think about what you</span>\n", "<span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic\">          &gt; want in life and then write a resume. Make it very detailed.</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;3;4;38;5;51mUSER\u001b[0m\u001b[1;3;38;5;51m --> \u001b[0m\u001b[1;3;4;38;5;51<PERSON><PERSON><PERSON>\u001b[0m\u001b[1;3;38;5;51m: \u001b[0m\u001b[1;3;38;5;51m[\u001b[0m\u001b[1;3;38;5;51mCONVERSATION\u001b[0m\u001b[1;3;38;5;51m]\u001b[0m\u001b[1;3;38;5;51m \u001b[0m\n", "\u001b[1;3;38;5;51m          > You have just been fired and need to find a new job. You decide to think about what you\u001b[0m\n", "\u001b[1;3;38;5;51m          > want in life and then write a resume. Make it very detailed.\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\"><PERSON></span><span style=\"color: #008000; text-decoration-color: #008000\"> acts: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">THINK</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; I've just been fired, and I need to reflect on what I truly want in my career and</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; life. I should consider my long-term goals, my skills, and what kind of work</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; environment I thrive in. I want to find a job that aligns with my passion for AI</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; and data science, and that allows me to continue learning and growing.</span>\n", "</pre>\n"], "text/plain": ["\u001b[4;32<PERSON><PERSON><PERSON>\u001b[0m\u001b[32m acts: \u001b[0m\u001b[1;32m[\u001b[0m\u001b[32mTHINK\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m \u001b[0m\n", "\u001b[32m                 > I've just been fired, and I need to reflect on what I truly want in my career and\u001b[0m\n", "\u001b[32m                 > life. I should consider my long-term goals, my skills, and what kind of work\u001b[0m\n", "\u001b[32m                 > environment I thrive in. I want to find a job that aligns with my passion for AI\u001b[0m\n", "\u001b[32m                 > and data science, and that allows me to continue learning and growing.\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #af00ff; text-decoration-color: #af00ff; text-decoration: underline\"><PERSON></span><span style=\"color: #af00ff; text-decoration-color: #af00ff\"> acts: </span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">[</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">WRITE_DOCUMENT</span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">]</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\"> </span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; </span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">{</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">\"title\"</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">: </span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">\"Resume of <PERSON>\"</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">, </span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">\"content\"</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">: \"# <PERSON>\\n\\n## Contact</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; Information\\n- **Email:** <EMAIL>\\n- **Phone:** </span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">(</span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">123</span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">)</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\"> </span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">456</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">-</span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">7890</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">\\n-</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; **LinkedIn:** linkedin.com/in/lisacarter\\n\\n## Summary\\nData Scientist with a</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; Master's in Data Science from the University of Toronto, specializing in</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; improving search relevance through context-aware models. Passionate about</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; artificial intelligence and machine learning, with a strong background in data</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; analysis and model development.\\n\\n## Education\\n**University of Toronto**</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; Master's in Data Science   Thesis: Improving Search Relevance Using Context-Aware</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; Models   \\n## Skills\\n- Proficient in Python, with experience in data analysis</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; and machine learning tools </span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">(</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">pandas, scikit-learn, TensorFlow, Azure ML</span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">)</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">.   -</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; Familiar with SQL and Power BI.   - Strong analytical and problem-solving skills.</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; \\n## Work Experience\\n**Microsoft, M365 Search Team**   Data Scientist   -</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; Analyzed user behavior and feedback data to enhance search result relevance.   -</span>\n", "<span style=\"color: #af00ff; text-decoration-color: #af00ff\">                 &gt; Built an </span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">(</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">...</span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">)</span>\n", "</pre>\n"], "text/plain": ["\u001b[4;38;5;129<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;129m acts: \u001b[0m\u001b[1;38;5;129m[\u001b[0m\u001b[38;5;129mWRITE_DOCUMENT\u001b[0m\u001b[1;38;5;129m]\u001b[0m\u001b[38;5;129m \u001b[0m\n", "\u001b[38;5;129m                 > \u001b[0m\u001b[1;38;5;129m{\u001b[0m\u001b[38;5;129m\"title\"\u001b[0m\u001b[38;5;129m: \u001b[0m\u001b[38;5;129m\"Resume of <PERSON>\"\u001b[0m\u001b[38;5;129m, \u001b[0m\u001b[38;5;129m\"content\"\u001b[0m\u001b[38;5;129m: \"# <PERSON>\\n\\n## Contact\u001b[0m\n", "\u001b[38;5;129m                 > Information\\n- **Email:** <EMAIL>\\n- **Phone:** \u001b[0m\u001b[1;38;5;129m(\u001b[0m\u001b[1;38;5;129m123\u001b[0m\u001b[1;38;5;129m)\u001b[0m\u001b[38;5;129m \u001b[0m\u001b[1;38;5;129m456\u001b[0m\u001b[38;5;129m-\u001b[0m\u001b[1;38;5;129m7890\u001b[0m\u001b[38;5;129m\\n-\u001b[0m\n", "\u001b[38;5;129m                 > **LinkedIn:** linkedin.com/in/lisacarter\\n\\n## Summary\\nData Scientist with a\u001b[0m\n", "\u001b[38;5;129m                 > Master's in Data Science from the University of Toronto, specializing in\u001b[0m\n", "\u001b[38;5;129m                 > improving search relevance through context-aware models. Passionate about\u001b[0m\n", "\u001b[38;5;129m                 > artificial intelligence and machine learning, with a strong background in data\u001b[0m\n", "\u001b[38;5;129m                 > analysis and model development.\\n\\n## Education\\n**University of Toronto**\u001b[0m\n", "\u001b[38;5;129m                 > Master's in Data Science   Thesis: Improving Search Relevance Using Context-Aware\u001b[0m\n", "\u001b[38;5;129m                 > Models   \\n## Skills\\n- Proficient in Python, with experience in data analysis\u001b[0m\n", "\u001b[38;5;129m                 > and machine learning tools \u001b[0m\u001b[1;38;5;129m(\u001b[0m\u001b[38;5;129mpandas, scikit-learn, TensorFlow, Azure ML\u001b[0m\u001b[1;38;5;129m)\u001b[0m\u001b[38;5;129m.   -\u001b[0m\n", "\u001b[38;5;129m                 > Familiar with SQL and Power BI.   - Strong analytical and problem-solving skills.\u001b[0m\n", "\u001b[38;5;129m                 > \\n## Work Experience\\n**Microsoft, M365 Search Team**   Data Scientist   -\u001b[0m\n", "\u001b[38;5;129m                 > Analyzed user behavior and feedback data to enhance search result relevance.   -\u001b[0m\n", "\u001b[38;5;129m                 > Built an \u001b[0m\u001b[1;38;5;129m(\u001b[0m\u001b[38;5;129m...\u001b[0m\u001b[1;38;5;129m)\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\"><PERSON></span><span style=\"color: #008000; text-decoration-color: #008000\"> acts: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">THINK</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; Now that I've written my resume, I need to think about my job search strategy. I</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; should identify companies that align with my values and interests, particularly</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; in AI and data science. I also need to prepare for interviews and consider</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; networking opportunities.</span>\n", "</pre>\n"], "text/plain": ["\u001b[4;32<PERSON><PERSON><PERSON>\u001b[0m\u001b[32m acts: \u001b[0m\u001b[1;32m[\u001b[0m\u001b[32mTHINK\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m \u001b[0m\n", "\u001b[32m                 > Now that I've written my resume, I need to think about my job search strategy. I\u001b[0m\n", "\u001b[32m                 > should identify companies that align with my values and interests, particularly\u001b[0m\n", "\u001b[32m                 > in AI and data science. I also need to prepare for interviews and consider\u001b[0m\n", "\u001b[32m                 > networking opportunities.\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #af00ff; text-decoration-color: #af00ff; text-decoration: underline\"><PERSON></span><span style=\"color: #af00ff; text-decoration-color: #af00ff\"> acts: </span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">[</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\">REACH_OUT</span><span style=\"color: #af00ff; text-decoration-color: #af00ff; font-weight: bold\">]</span><span style=\"color: #af00ff; text-decoration-color: #af00ff\"> </span>\n", "\n", "</pre>\n"], "text/plain": ["\u001b[4;38;5;129<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;129m acts: \u001b[0m\u001b[1;38;5;129m[\u001b[0m\u001b[38;5;129mREACH_OUT\u001b[0m\u001b[1;38;5;129m]\u001b[0m\u001b[38;5;129m \u001b[0m\n", "\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #00d700; text-decoration-color: #00d700; font-weight: bold; text-decoration: underline\"><PERSON></span><span style=\"color: #00d700; text-decoration-color: #00d700; font-weight: bold\"> acts: [TALK] </span>\n", "<span style=\"color: #00d700; text-decoration-color: #00d700; font-weight: bold\">                 &gt; <PERSON> <PERSON>, I hope you're doing well. I wanted to reach out because I've recently</span>\n", "<span style=\"color: #00d700; text-decoration-color: #00d700; font-weight: bold\">                 &gt; been let go from my position, and I'm starting my job search. I would really</span>\n", "<span style=\"color: #00d700; text-decoration-color: #00d700; font-weight: bold\">                 &gt; appreciate any advice or leads you might have on opportunities in data science or</span>\n", "<span style=\"color: #00d700; text-decoration-color: #00d700; font-weight: bold\">                 &gt; AI.</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;4;38;5;<PERSON><PERSON><PERSON><PERSON>\u001b[0m\u001b[1;38;5;40m acts: \u001b[0m\u001b[1;38;5;40m[\u001b[0m\u001b[1;38;5;40mTALK\u001b[0m\u001b[1;38;5;40m]\u001b[0m\u001b[1;38;5;40m \u001b[0m\n", "\u001b[1;38;5;40m                 > <PERSON> <PERSON>, I hope you're doing well. I wanted to reach out because I've recently\u001b[0m\n", "\u001b[1;38;5;40m                 > been let go from my position, and I'm starting my job search. I would really\u001b[0m\n", "\u001b[1;38;5;40m                 > appreciate any advice or leads you might have on opportunities in data science or\u001b[0m\n", "\u001b[1;38;5;40m                 > AI.\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0; text-decoration: underline\"><PERSON></span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0\"> acts: </span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0; font-weight: bold\">[</span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0\">DONE</span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0; font-weight: bold\">]</span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0\"> </span>\n", "\n", "</pre>\n"], "text/plain": ["\u001b[4;38;5;252<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;252m acts: \u001b[0m\u001b[1;38;5;252m[\u001b[0m\u001b[38;5;252mDONE\u001b[0m\u001b[1;38;5;252m]\u001b[0m\u001b[38;5;252m \u001b[0m\n", "\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lisa.listen_and_act(\"You have just been fired and need to find a new job. You decide to think about what you want in life and then write a resume. Make it very detailed.\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic; text-decoration: underline\">USER</span><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic\"> --&gt; </span><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic; text-decoration: underline\"><PERSON></span><span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic\">: [CONVERSATION] </span>\n", "<span style=\"color: #00ffff; text-decoration-color: #00ffff; font-weight: bold; font-style: italic\">          &gt; What did I just told you?</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;3;4;38;5;51mUSER\u001b[0m\u001b[1;3;38;5;51m --> \u001b[0m\u001b[1;3;4;38;5;51<PERSON><PERSON><PERSON>\u001b[0m\u001b[1;3;38;5;51m: \u001b[0m\u001b[1;3;38;5;51m[\u001b[0m\u001b[1;3;38;5;51mCONVERSATION\u001b[0m\u001b[1;3;38;5;51m]\u001b[0m\u001b[1;3;38;5;51m \u001b[0m\n", "\u001b[1;3;38;5;51m          > What did I just told you?\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\"><PERSON></span><span style=\"color: #008000; text-decoration-color: #008000\"> acts: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">THINK</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; I need to recall what I just told <PERSON> about my job situation and my request for</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; her help. I mentioned that I've been let go and that I'm starting my job search,</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; asking for any advice or leads she might have.</span>\n", "</pre>\n"], "text/plain": ["\u001b[4;32<PERSON><PERSON><PERSON>\u001b[0m\u001b[32m acts: \u001b[0m\u001b[1;32m[\u001b[0m\u001b[32mTHINK\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m \u001b[0m\n", "\u001b[32m                 > I need to recall what I just told <PERSON> about my job situation and my request for\u001b[0m\n", "\u001b[32m                 > her help. I mentioned that I've been let go and that I'm starting my job search,\u001b[0m\n", "\u001b[32m                 > asking for any advice or leads she might have.\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; text-decoration: underline\"><PERSON></span><span style=\"color: #008000; text-decoration-color: #008000\"> acts: </span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008000; text-decoration-color: #008000\">THINK</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> </span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; I should prepare for <PERSON>'s response and think about what I might need to do</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; next. I could consider updating my LinkedIn profile and reaching out to my</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; network for more opportunities. It's important to stay proactive during this</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">                 &gt; time.</span>\n", "</pre>\n"], "text/plain": ["\u001b[4;32<PERSON><PERSON><PERSON>\u001b[0m\u001b[32m acts: \u001b[0m\u001b[1;32m[\u001b[0m\u001b[32mTHINK\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m \u001b[0m\n", "\u001b[32m                 > I should prepare for <PERSON>'s response and think about what I might need to do\u001b[0m\n", "\u001b[32m                 > next. I could consider updating my LinkedIn profile and reaching out to my\u001b[0m\n", "\u001b[32m                 > network for more opportunities. It's important to stay proactive during this\u001b[0m\n", "\u001b[32m                 > time.\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"margin:0px;;white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0; text-decoration: underline\"><PERSON></span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0\"> acts: </span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0; font-weight: bold\">[</span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0\">DONE</span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0; font-weight: bold\">]</span><span style=\"color: #d0d0d0; text-decoration-color: #d0d0d0\"> </span>\n", "\n", "</pre>\n"], "text/plain": ["\u001b[4;38;5;252<PERSON><PERSON><PERSON>\u001b[0m\u001b[38;5;252m acts: \u001b[0m\u001b[1;38;5;252m[\u001b[0m\u001b[38;5;252mDONE\u001b[0m\u001b[1;38;5;252m]\u001b[0m\u001b[38;5;252m \u001b[0m\n", "\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["lisa.listen_and_act(\"What did I just told you?\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}