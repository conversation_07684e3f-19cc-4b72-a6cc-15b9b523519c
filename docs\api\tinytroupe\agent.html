<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>tinytroupe.agent API documentation</title>
<meta name="description" content="This module provides the main classes and functions for TinyTroupe&#39;s
agents …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>tinytroupe.agent</code></h1>
</header>
<section id="section-intro">
<p>This module provides the main classes and functions for TinyTroupe's
agents.</p>
<p>Agents are the key abstraction used in TinyTroupe. An agent is a simulated person or entity that can interact with other agents and the environment, by
receiving stimuli and producing actions. Agents have cognitive states, which are updated as they interact with the environment and other agents.
Agents can also store and retrieve information from memory, and can perform actions in the environment. Different from agents whose objective is to
provide support for AI-based assistants or other such productivity tools, <strong>TinyTroupe agents are aim at representing human-like behavior</strong>, which includes
idiossincracies, emotions, and other human-like traits, that one would not expect from a productivity tool.</p>
<p>The overall underlying design is inspired mainly by cognitive psychology, which is why agents have various internal cognitive states, such as attention, emotions, and goals.
It is also why agent memory, differently from other LLM-based agent platforms, has subtle internal divisions, notably between episodic and semantic memory.
Some behaviorist concepts are also present, such as the idea of a "stimulus" and "response" in the <code>listen</code> and <code>act</code> methods, which are key abstractions
to understand how agents interact with the environment and other agents.</p>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">&#34;&#34;&#34;
This module provides the main classes and functions for TinyTroupe&#39;s  agents.

Agents are the key abstraction used in TinyTroupe. An agent is a simulated person or entity that can interact with other agents and the environment, by
receiving stimuli and producing actions. Agents have cognitive states, which are updated as they interact with the environment and other agents. 
Agents can also store and retrieve information from memory, and can perform actions in the environment. Different from agents whose objective is to
provide support for AI-based assistants or other such productivity tools, **TinyTroupe agents are aim at representing human-like behavior**, which includes
idiossincracies, emotions, and other human-like traits, that one would not expect from a productivity tool.

The overall underlying design is inspired mainly by cognitive psychology, which is why agents have various internal cognitive states, such as attention, emotions, and goals.
It is also why agent memory, differently from other LLM-based agent platforms, has subtle internal divisions, notably between episodic and semantic memory. 
Some behaviorist concepts are also present, such as the idea of a &#34;stimulus&#34; and &#34;response&#34; in the `listen` and `act` methods, which are key abstractions
to understand how agents interact with the environment and other agents.
&#34;&#34;&#34;

import os
import csv
import json
import ast
import textwrap  # to dedent strings
import datetime  # to get current datetime
import chevron  # to parse Mustache templates
import logging
logger = logging.getLogger(&#34;tinytroupe&#34;)
import tinytroupe.utils as utils
from tinytroupe.utils import post_init
from tinytroupe.control import transactional
from tinytroupe.control import current_simulation
from rich import print
import copy
from tinytroupe.utils import JsonSerializableRegistry

from typing import Any, TypeVar, Union

Self = TypeVar(&#34;Self&#34;, bound=&#34;TinyPerson&#34;)
AgentOrWorld = Union[Self, &#34;TinyWorld&#34;]

###########################################################################
# Default parameter values
###########################################################################
# We&#39;ll use various configuration elements below
config = utils.read_config_file()

default = {}
default[&#34;embedding_model&#34;] = config[&#34;OpenAI&#34;].get(&#34;EMBEDDING_MODEL&#34;, &#34;text-embedding-3-small&#34;)
default[&#34;max_content_display_length&#34;] = config[&#34;OpenAI&#34;].getint(&#34;MAX_CONTENT_DISPLAY_LENGTH&#34;, 1024)


## LLaMa-Index configs ########################################################
#from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core import Settings, VectorStoreIndex, SimpleDirectoryReader
from llama_index.readers.web import SimpleWebPageReader


# this will be cached locally by llama-index, in a OS-dependend location

##Settings.embed_model = HuggingFaceEmbedding(
##    model_name=&#34;BAAI/bge-small-en-v1.5&#34;
##)

llmaindex_openai_embed_model = OpenAIEmbedding(model=default[&#34;embedding_model&#34;], embed_batch_size=10)
Settings.embed_model = llmaindex_openai_embed_model
###############################################################################


from tinytroupe import openai_utils
from tinytroupe.utils import name_or_empty, break_text_at_length, repeat_on_error


#######################################################################################################################
# TinyPerson itself
#######################################################################################################################
@post_init
class TinyPerson(JsonSerializableRegistry):
    &#34;&#34;&#34;A simulated person in the TinyTroupe universe.&#34;&#34;&#34;

    # The maximum number of actions that an agent is allowed to perform before DONE.
    # This prevents the agent from acting without ever stopping.
    MAX_ACTIONS_BEFORE_DONE = 15

    PP_TEXT_WIDTH = 100

    serializable_attributes = [&#34;name&#34;, &#34;episodic_memory&#34;, &#34;semantic_memory&#34;, &#34;_mental_faculties&#34;, &#34;_configuration&#34;]

    # A dict of all agents instantiated so far.
    all_agents = {}  # name -&gt; agent

    # The communication style for all agents: &#34;simplified&#34; or &#34;full&#34;.
    communication_style:str=&#34;simplified&#34;
    
    # Whether to display the communication or not. True is for interactive applications, when we want to see simulation
    # outputs as they are produced.
    communication_display:bool=True
    

    def __init__(self, name:str=None, 
                 episodic_memory=None,
                 semantic_memory=None,
                 mental_faculties:list=None):
        &#34;&#34;&#34;
        Creates a TinyPerson.

        Args:
            name (str): The name of the TinyPerson. Either this or spec_path must be specified.
            episodic_memory (EpisodicMemory, optional): The memory implementation to use. Defaults to EpisodicMemory().
            semantic_memory (SemanticMemory, optional): The memory implementation to use. Defaults to SemanticMemory().
            mental_faculties (list, optional): A list of mental faculties to add to the agent. Defaults to None.
        &#34;&#34;&#34;

        # NOTE: default values will be given in the _post_init method, as that&#39;s shared by 
        #       direct initialization as well as via deserialization.

        if episodic_memory is not None:
            self.episodic_memory = episodic_memory
        
        if semantic_memory is not None:
            self.semantic_memory = semantic_memory

        # Mental faculties
        if mental_faculties is not None:
            self._mental_faculties = mental_faculties
        
        assert name is not None, &#34;A TinyPerson must have a name.&#34;
        self.name = name

        # @post_init makes sure that _post_init is called after __init__

    
    def _post_init(self, **kwargs):
        &#34;&#34;&#34;
        This will run after __init__, since the class has the @post_init decorator.
        It is convenient to separate some of the initialization processes to make deserialize easier.
        &#34;&#34;&#34;

        ############################################################
        # Default values
        ############################################################

        self.current_messages = []
        
        # the current environment in which the agent is acting
        self.environment = None

        # The list of actions that this agent has performed so far, but which have not been
        # consumed by the environment yet.
        self._actions_buffer = []

        # The list of agents that this agent can currently interact with.
        # This can change over time, as agents move around the world.
        self._accessible_agents = []

        # the buffer of communications that have been displayed so far, used for
        # saving these communications to another output form later (e.g., caching)
        self._displayed_communications_buffer = []

        if not hasattr(self, &#39;episodic_memory&#39;):
            # This default value MUST NOT be in the method signature, otherwise it will be shared across all instances.
            self.episodic_memory = EpisodicMemory()
        
        if not hasattr(self, &#39;semantic_memory&#39;):
            # This default value MUST NOT be in the method signature, otherwise it will be shared across all instances.
            self.semantic_memory = SemanticMemory()
        
        # _mental_faculties
        if not hasattr(self, &#39;_mental_faculties&#39;):
            # This default value MUST NOT be in the method signature, otherwise it will be shared across all instances.
            self._mental_faculties = []

        # create the configuration dictionary
        if not hasattr(self, &#39;_configuration&#39;):          
            self._configuration = {
                &#34;name&#34;: self.name,
                &#34;age&#34;: None,
                &#34;nationality&#34;: None,
                &#34;country_of_residence&#34;: None,
                &#34;occupation&#34;: None,
                &#34;routines&#34;: [],
                &#34;occupation_description&#34;: None,
                &#34;personality_traits&#34;: [],
                &#34;professional_interests&#34;: [],
                &#34;personal_interests&#34;: [],
                &#34;skills&#34;: [],
                &#34;relationships&#34;: [],
                &#34;current_datetime&#34;: None,
                &#34;current_location&#34;: None,
                &#34;current_context&#34;: [],
                &#34;current_attention&#34;: None,
                &#34;current_goals&#34;: [],
                &#34;current_emotions&#34;: &#34;Currently you feel calm and friendly.&#34;,
                &#34;currently_accessible_agents&#34;: [],  # [{&#34;agent&#34;: agent_1, &#34;relation&#34;: &#34;My friend&#34;}, {&#34;agent&#34;: agent_2, &#34;relation&#34;: &#34;My colleague&#34;}, ...]
            }

        self._prompt_template_path = os.path.join(
            os.path.dirname(__file__), &#34;prompts/tinyperson.mustache&#34;
        )
        self._init_system_message = None  # initialized later


        ############################################################
        # Special mechanisms used during deserialization
        ############################################################

        # rename agent to some specific name?
        if kwargs.get(&#34;new_agent_name&#34;) is not None:
            self._rename(kwargs.get(&#34;new_agent_name&#34;))
        
        # If auto-rename, use the given name plus some new number ...
        if kwargs.get(&#34;auto_rename&#34;) is True:
            new_name = self.name # start with the current name
            rename_succeeded = False
            while not rename_succeeded:
                try:
                    self._rename(new_name)
                    TinyPerson.add_agent(self)
                    rename_succeeded = True                
                except ValueError:
                    new_id = utils.fresh_id()
                    new_name = f&#34;{self.name}_{new_id}&#34;
        
        # ... otherwise, just register the agent
        else:
            # register the agent in the global list of agents
            TinyPerson.add_agent(self)

        # start with a clean slate
        self.reset_prompt()

        # it could be the case that the agent is being created within a simulation scope, in which case
        # the simulation_id must be set accordingly
        if current_simulation() is not None:
            current_simulation().add_agent(self)
        else:
            self.simulation_id = None
    
    def _rename(self, new_name:str):    
        self.name = new_name
        self._configuration[&#34;name&#34;] = self.name


    def generate_agent_prompt(self):
        with open(self._prompt_template_path, &#34;r&#34;) as f:
            agent_prompt_template = f.read()

        # let&#39;s operate on top of a copy of the configuration, because we&#39;ll need to add more variables, etc.
        template_variables = self._configuration.copy()    

        # Prepare additional action definitions and constraints
        actions_definitions_prompt = &#34;&#34;
        actions_constraints_prompt = &#34;&#34;
        for faculty in self._mental_faculties:
            actions_definitions_prompt += f&#34;{faculty.actions_definitions_prompt()}\n&#34;
            actions_constraints_prompt += f&#34;{faculty.actions_constraints_prompt()}\n&#34;
        
        # make the additional prompt pieces available to the template
        template_variables[&#39;actions_definitions_prompt&#39;] = textwrap.indent(actions_definitions_prompt, &#34;&#34;)
        template_variables[&#39;actions_constraints_prompt&#39;] = textwrap.indent(actions_constraints_prompt, &#34;&#34;)

        # RAI prompt components, if requested
        template_variables = utils.add_rai_template_variables_if_enabled(template_variables)

        return chevron.render(agent_prompt_template, template_variables)

    def reset_prompt(self):

        # render the template with the current configuration
        self._init_system_message = self.generate_agent_prompt()

        # TODO actually, figure out another way to update agent state without &#34;changing history&#34;

        # reset system message
        self.current_messages = [
            {&#34;role&#34;: &#34;system&#34;, &#34;content&#34;: self._init_system_message}
        ]

        # sets up the actual interaction messages to use for prompting
        self.current_messages += self.episodic_memory.retrieve_recent()

    def get(self, key):
        &#34;&#34;&#34;
        Returns the definition of a key in the TinyPerson&#39;s configuration.
        &#34;&#34;&#34;
        return self._configuration.get(key, None)
    
    @transactional
    def define(self, key, value, group=None):
        &#34;&#34;&#34;
        Define a value to the TinyPerson&#39;s configuration.
        If group is None, the value is added to the top level of the configuration.
        Otherwise, the value is added to the specified group.
        &#34;&#34;&#34;

        # dedent value if it is a string
        if isinstance(value, str):
            value = textwrap.dedent(value)

        if group is None:
            # logger.debug(f&#34;[{self.name}] Defining {key}={value} in the person.&#34;)
            self._configuration[key] = value
        else:
            if key is not None:
                # logger.debug(f&#34;[{self.name}] Adding definition to {group} += [ {key}={value} ] in the person.&#34;)
                self._configuration[group].append({key: value})
            else:
                # logger.debug(f&#34;[{self.name}] Adding definition to {group} += [ {value} ] in the person.&#34;)
                self._configuration[group].append(value)

        # must reset prompt after adding to configuration
        self.reset_prompt()

    def define_several(self, group, records):
        &#34;&#34;&#34;
        Define several values to the TinyPerson&#39;s configuration, all belonging to the same group.
        &#34;&#34;&#34;
        for record in records:
            self.define(key=None, value=record, group=group)
    
    @transactional
    def define_relationships(self, relationships, replace=True):
        &#34;&#34;&#34;
        Defines or updates the TinyPerson&#39;s relationships.

        Args:
            relationships (list or dict): The relationships to add or replace. Either a list of dicts mapping agent names to relationship descriptions,
              or a single dict mapping one agent name to its relationship description.
            replace (bool, optional): Whether to replace the current relationships or just add to them. Defaults to True.
        &#34;&#34;&#34;
        
        if (replace == True) and (isinstance(relationships, list)):
            self._configuration[&#39;relationships&#39;] = relationships

        elif replace == False:
            current_relationships = self._configuration[&#39;relationships&#39;]
            if isinstance(relationships, list):
                for r in relationships:
                    current_relationships.append(r)
                
            elif isinstance(relationships, dict) and len(relationships) == 2: #{&#34;Name&#34;: ..., &#34;Description&#34;: ...}
                current_relationships.append(relationships)

            else:
                raise Exception(&#34;Only one key-value pair is allowed in the relationships dict.&#34;)

        else:
            raise Exception(&#34;Invalid arguments for define_relationships.&#34;)

    @transactional
    def clear_relationships(self):
        &#34;&#34;&#34;
        Clears the TinyPerson&#39;s relationships.
        &#34;&#34;&#34;
        self._configuration[&#39;relationships&#39;] = []  

        return self      
    
    @transactional
    def related_to(self, other_agent, description, symmetric_description=None):
        &#34;&#34;&#34;
        Defines a relationship between this agent and another agent.

        Args:
            other_agent (TinyPerson): The other agent.
            description (str): The description of the relationship.
            symmetric (bool): Whether the relationship is symmetric or not. That is, 
              if the relationship is defined for both agents.
        
        Returns:
            TinyPerson: The agent itself, to facilitate chaining.
        &#34;&#34;&#34;
        self.define_relationships([{&#34;Name&#34;: other_agent.name, &#34;Description&#34;: description}], replace=False)
        if symmetric_description is not None:
            other_agent.define_relationships([{&#34;Name&#34;: self.name, &#34;Description&#34;: symmetric_description}], replace=False)
        
        return self
    
    def add_mental_faculties(self, mental_faculties):
        &#34;&#34;&#34;
        Adds a list of mental faculties to the agent.
        &#34;&#34;&#34;
        for faculty in mental_faculties:
            self.add_mental_faculty(faculty)
        
        return self

    def add_mental_faculty(self, faculty):
        &#34;&#34;&#34;
        Adds a mental faculty to the agent.
        &#34;&#34;&#34;
        # check if the faculty is already there or not
        if faculty not in self._mental_faculties:
            self._mental_faculties.append(faculty)
        else:
            raise Exception(f&#34;The mental faculty {faculty} is already present in the agent.&#34;)
        
        return self

    @transactional
    def act(
        self,
        until_done=True,
        n=None,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Acts in the environment and updates its internal cognitive state.
        Either acts until the agent is done and needs additional stimuli, or acts a fixed number of times,
        but not both.

        Args:
            until_done (bool): Whether to keep acting until the agent is done and needs additional stimuli.
            n (int): The number of actions to perform. Defaults to None.
            return_actions (bool): Whether to return the actions or not. Defaults to False.
        &#34;&#34;&#34;

        # either act until done or act a fixed number of times, but not both
        assert not (until_done and n is not None)
        if n is not None:
            assert n &lt; TinyPerson.MAX_ACTIONS_BEFORE_DONE

        contents = []

        # Aux function to perform exactly one action.
        # Occasionally, the model will return JSON missing important keys, so we just ask it to try again
        @repeat_on_error(retries=5, exceptions=[KeyError])
        def aux_act_once():
            # A quick thought before the action. This seems to help with better model responses, perhaps because
            # it interleaves user with assistant messages.
            self.think(&#34;I will now act a bit, and then issue DONE.&#34;)

          
            role, content = self._produce_message()

            self.episodic_memory.store({&#39;role&#39;: role, &#39;content&#39;: content, &#39;simulation_timestamp&#39;: self.iso_datetime()})

            cognitive_state = content[&#34;cognitive_state&#34;]


            action = content[&#39;action&#39;]

            self._actions_buffer.append(action)
            self._update_cognitive_state(goals=cognitive_state[&#39;goals&#39;],
                                        attention=cognitive_state[&#39;attention&#39;],
                                        emotions=cognitive_state[&#39;emotions&#39;])
            
            contents.append(content)          
            if TinyPerson.communication_display:
                self._display_communication(role=role, content=content, kind=&#39;action&#39;, simplified=True, max_content_length=max_content_length)
            
            #
            # Some actions induce an immediate stimulus or other side-effects. We need to process them here, by means of the mental faculties.
            #
            for faculty in self._mental_faculties:
                faculty.process_action(self, action)             
            

        #
        # How to proceed with a sequence of actions.
        #

        ##### Option 1: run N actions ######
        if n is not None:
            for i in range(n):
                aux_act_once()

        ##### Option 2: run until DONE ######
        elif until_done:
            while (len(contents) == 0) or (
                not contents[-1][&#34;action&#34;][&#34;type&#34;] == &#34;DONE&#34;
            ):


                # check if the agent is acting without ever stopping
                if len(contents) &gt; TinyPerson.MAX_ACTIONS_BEFORE_DONE:
                    logger.warning(f&#34;[{self.name}] Agent {self.name} is acting without ever stopping. This may be a bug. Let&#39;s stop it here anyway.&#34;)
                    break
                if len(contents) &gt; 4: # just some minimum number of actions to check for repetition, could be anything &gt;= 3
                    # if the last three actions were the same, then we are probably in a loop
                    if contents[-1][&#39;action&#39;] == contents[-2][&#39;action&#39;] == contents[-3][&#39;action&#39;]:
                        logger.warning(f&#34;[{self.name}] Agent {self.name} is acting in a loop. This may be a bug. Let&#39;s stop it here anyway.&#34;)
                        break

                aux_act_once()

        if return_actions:
            return contents

    @transactional
    def listen(
        self,
        speech,
        source: AgentOrWorld = None,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Listens to another agent (artificial or human) and updates its internal cognitive state.

        Args:
            speech (str): The speech to listen to.
            source (AgentOrWorld, optional): The source of the speech. Defaults to None.
        &#34;&#34;&#34;

        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;CONVERSATION&#34;,
                &#34;content&#34;: speech,
                &#34;source&#34;: name_or_empty(source),
            },
            max_content_length=max_content_length,
        )

    def socialize(
        self,
        social_description: str,
        source: AgentOrWorld = None,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Perceives a social stimulus through a description and updates its internal cognitive state.

        Args:
            social_description (str): The description of the social stimulus.
            source (AgentOrWorld, optional): The source of the social stimulus. Defaults to None.
        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;SOCIAL&#34;,
                &#34;content&#34;: social_description,
                &#34;source&#34;: name_or_empty(source),
            },
            max_content_length=max_content_length,
        )

    def see(
        self,
        visual_description,
        source: AgentOrWorld = None,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Perceives a visual stimulus through a description and updates its internal cognitive state.

        Args:
            visual_description (str): The description of the visual stimulus.
            source (AgentOrWorld, optional): The source of the visual stimulus. Defaults to None.
        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;VISUAL&#34;,
                &#34;content&#34;: visual_description,
                &#34;source&#34;: name_or_empty(source),
            },
            max_content_length=max_content_length,
        )

    def think(self, thought, max_content_length=default[&#34;max_content_display_length&#34;]):
        &#34;&#34;&#34;
        Forces the agent to think about something and updates its internal cognitive state.

        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;THOUGHT&#34;,
                &#34;content&#34;: thought,
                &#34;source&#34;: name_or_empty(self),
            },
            max_content_length=max_content_length,
        )

    def internalize_goal(
        self, goal, max_content_length=default[&#34;max_content_display_length&#34;]
    ):
        &#34;&#34;&#34;
        Internalizes a goal and updates its internal cognitive state.
        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;INTERNAL_GOAL_FORMULATION&#34;,
                &#34;content&#34;: goal,
                &#34;source&#34;: name_or_empty(self),
            },
            max_content_length=max_content_length,
        )

    @transactional
    def _observe(self, stimulus, max_content_length=default[&#34;max_content_display_length&#34;]):
        stimuli = [stimulus]

        content = {&#34;stimuli&#34;: stimuli}

        logger.debug(f&#34;[{self.name}] Observing stimuli: {content}&#34;)

        # whatever comes from the outside will be interpreted as coming from &#39;user&#39;, simply because
        # this is the counterpart of &#39;assistant&#39;

        self.episodic_memory.store({&#39;role&#39;: &#39;user&#39;, &#39;content&#39;: content, &#39;simulation_timestamp&#39;: self.iso_datetime()})

        if TinyPerson.communication_display:
            self._display_communication(
                role=&#34;user&#34;,
                content=content,
                kind=&#34;stimuli&#34;,
                simplified=True,
                max_content_length=max_content_length,
            )

        return self  # allows easier chaining of methods

    @transactional
    def listen_and_act(
        self,
        speech,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Convenience method that combines the `listen` and `act` methods.
        &#34;&#34;&#34;

        self.listen(speech, max_content_length=max_content_length)
        return self.act(
            return_actions=return_actions, max_content_length=max_content_length
        )

    @transactional
    def see_and_act(
        self,
        visual_description,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Convenience method that combines the `see` and `act` methods.
        &#34;&#34;&#34;

        self.see(visual_description, max_content_length=max_content_length)
        return self.act(
            return_actions=return_actions, max_content_length=max_content_length
        )

    @transactional
    def think_and_act(
        self,
        thought,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Convenience method that combines the `think` and `act` methods.
        &#34;&#34;&#34;

        self.think(thought, max_content_length=max_content_length)
        return self.act(return_actions=return_actions, max_content_length=max_content_length)

    def read_documents_from_folder(self, documents_path:str):
        &#34;&#34;&#34;
        Reads documents from a directory and loads them into the semantic memory.
        &#34;&#34;&#34;
        logger.info(f&#34;Setting documents path to {documents_path} and loading documents.&#34;)

        self.semantic_memory.add_documents_path(documents_path)
    
    def read_documents_from_web(self, web_urls:list):
        &#34;&#34;&#34;
        Reads documents from web URLs and loads them into the semantic memory.
        &#34;&#34;&#34;
        logger.info(f&#34;Reading documents from the following web URLs: {web_urls}&#34;)

        self.semantic_memory.add_web_urls(web_urls)
    
    @transactional
    def move_to(self, location, context=[]):
        &#34;&#34;&#34;
        Moves to a new location and updates its internal cognitive state.
        &#34;&#34;&#34;
        self._configuration[&#34;current_location&#34;] = location

        # context must also be updated when moved, since we assume that context is dictated partly by location.
        self.change_context(context)

    @transactional
    def change_context(self, context: list):
        &#34;&#34;&#34;
        Changes the context and updates its internal cognitive state.
        &#34;&#34;&#34;
        self._configuration[&#34;current_context&#34;] = {
            &#34;description&#34;: item for item in context
        }

        self._update_cognitive_state(context=context)

    @transactional
    def make_agent_accessible(
        self,
        agent: Self,
        relation_description: str = &#34;An agent I can currently interact with.&#34;,
    ):
        &#34;&#34;&#34;
        Makes an agent accessible to this agent.
        &#34;&#34;&#34;
        if agent not in self._accessible_agents:
            self._accessible_agents.append(agent)
            self._configuration[&#34;currently_accessible_agents&#34;].append(
                {&#34;name&#34;: agent.name, &#34;relation_description&#34;: relation_description}
            )
        else:
            logger.warning(
                f&#34;[{self.name}] Agent {agent.name} is already accessible to {self.name}.&#34;
            )

    @transactional
    def make_agent_inaccessible(self, agent: Self):
        &#34;&#34;&#34;
        Makes an agent inaccessible to this agent.
        &#34;&#34;&#34;
        if agent in self._accessible_agents:
            self._accessible_agents.remove(agent)
        else:
            logger.warning(
                f&#34;[{self.name}] Agent {agent.name} is already inaccessible to {self.name}.&#34;
            )

    @transactional
    def make_all_agents_inaccessible(self):
        &#34;&#34;&#34;
        Makes all agents inaccessible to this agent.
        &#34;&#34;&#34;
        self._accessible_agents = []
        self._configuration[&#34;currently_accessible_agents&#34;] = []

    @transactional
    def _produce_message(self):
        # logger.debug(f&#34;Current messages: {self.current_messages}&#34;)

        # ensure we have the latest prompt (initial system message + selected messages from memory)
        self.reset_prompt()

        messages = [
            {&#34;role&#34;: msg[&#34;role&#34;], &#34;content&#34;: json.dumps(msg[&#34;content&#34;])}
            for msg in self.current_messages
        ]

        logger.debug(f&#34;[{self.name}] Sending messages to OpenAI API&#34;)
        logger.debug(f&#34;[{self.name}] Last interaction: {messages[-1]}&#34;)

        next_message = openai_utils.client().send_message(messages)

        logger.debug(f&#34;[{self.name}] Received message: {next_message}&#34;)

        return next_message[&#34;role&#34;], utils.extract_json(next_message[&#34;content&#34;])

    ###########################################################
    # Internal cognitive state changes
    ###########################################################
    @transactional
    def _update_cognitive_state(
        self, goals=None, context=None, attention=None, emotions=None
    ):
        &#34;&#34;&#34;
        Update the TinyPerson&#39;s cognitive state.
        &#34;&#34;&#34;

        # Update current datetime. The passage of time is controlled by the environment, if any.
        if self.environment is not None and self.environment.current_datetime is not None:
            self._configuration[&#34;current_datetime&#34;] = utils.pretty_datetime(self.environment.current_datetime)

        # update current goals
        if goals is not None:
            self._configuration[&#34;current_goals&#34;] = goals

        # update current context
        if context is not None:
            self._configuration[&#34;current_context&#34;] = context

        # update current attention
        if attention is not None:
            self._configuration[&#34;current_attention&#34;] = attention

        # update current emotions
        if emotions is not None:
            self._configuration[&#34;current_emotions&#34;] = emotions

        self.reset_prompt()

    ###########################################################
    # Inspection conveniences
    ###########################################################
    def _display_communication(
        self,
        role,
        content,
        kind,
        simplified=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Displays the current communication and stores it in a buffer for later use.
        &#34;&#34;&#34;
        if kind == &#34;stimuli&#34;:
            rendering = self._pretty_stimuli(
                role=role,
                content=content,
                simplified=simplified,
                max_content_length=max_content_length,
            )
        elif kind == &#34;action&#34;:
            rendering = self._pretty_action(
                role=role,
                content=content,
                simplified=simplified,
                max_content_length=max_content_length,
            )
        else:
            raise ValueError(f&#34;Unknown communication kind: {kind}&#34;)

        # if the agent has no parent environment, then it is a free agent and we can display the communication.
        # otherwise, the environment will display the communication instead. This is important to make sure that
        # the communication is displayed in the correct order, since environments control the flow of their underlying
        # agents.
        if self.environment is None:
            self._push_and_display_latest_communication(rendering)
        else:
            self.environment._push_and_display_latest_communication(rendering)

    def _push_and_display_latest_communication(self, rendering):
        &#34;&#34;&#34;
        Pushes the latest communications to the agent&#39;s buffer.
        &#34;&#34;&#34;
        self._displayed_communications_buffer.append(rendering)
        print(rendering)

    def pop_and_display_latest_communications(self):
        &#34;&#34;&#34;
        Pops the latest communications and displays them.
        &#34;&#34;&#34;
        communications = self._displayed_communications_buffer
        self._displayed_communications_buffer = []

        for communication in communications:
            print(communication)

        return communications

    def clear_communications_buffer(self):
        &#34;&#34;&#34;
        Cleans the communications buffer.
        &#34;&#34;&#34;
        self._displayed_communications_buffer = []

    @transactional
    def pop_latest_actions(self) -&gt; list:
        &#34;&#34;&#34;
        Returns the latest actions performed by this agent. Typically used
        by an environment to consume the actions and provide the appropriate
        environmental semantics to them (i.e., effects on other agents).
        &#34;&#34;&#34;
        actions = self._actions_buffer
        self._actions_buffer = []
        return actions

    @transactional
    def pop_actions_and_get_contents_for(
        self, action_type: str, only_last_action: bool = True
    ) -&gt; list:
        &#34;&#34;&#34;
        Returns the contents of actions of a given type performed by this agent.
        Typically used to perform inspections and tests.

        Args:
            action_type (str): The type of action to look for.
            only_last_action (bool, optional): Whether to only return the contents of the last action. Defaults to False.
        &#34;&#34;&#34;
        actions = self.pop_latest_actions()
        # Filter the actions by type
        actions = [action for action in actions if action[&#34;type&#34;] == action_type]

        # If interested only in the last action, return the latest one
        if only_last_action:
            return actions[-1].get(&#34;content&#34;, &#34;&#34;)

        # Otherwise, return all contents from the filtered actions
        return &#34;\n&#34;.join([action.get(&#34;content&#34;, &#34;&#34;) for action in actions])

    #############################################################################################
    # Formatting conveniences
    #
    # For rich colors,
    #    see: https://rich.readthedocs.io/en/latest/appendix/colors.html#appendix-colors
    #############################################################################################

    def __repr__(self):
        return f&#34;TinyPerson(name=&#39;{self.name}&#39;)&#34;

    def minibio(self):
        &#34;&#34;&#34;
        Returns a mini-biography of the TinyPerson.
        &#34;&#34;&#34;
        return f&#34;{self.name} is a {self._configuration[&#39;age&#39;]} year old {self._configuration[&#39;occupation&#39;]}, {self._configuration[&#39;nationality&#39;]}, currently living in {self._configuration[&#39;country_of_residence&#39;]}.&#34;

    def pp_current_interactions(
        self,
        simplified=True,
        skip_system=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Pretty prints the current messages.
        &#34;&#34;&#34;
        print(
            self.pretty_current_interactions(
                simplified=simplified,
                skip_system=skip_system,
                max_content_length=max_content_length,
            )
        )

    def pretty_current_interactions(self, simplified=True, skip_system=True, max_content_length=default[&#34;max_content_display_length&#34;], first_n=None, last_n=None, include_omission_info:bool=True):
      &#34;&#34;&#34;
      Returns a pretty, readable, string with the current messages.
      &#34;&#34;&#34;
      lines = []
      for message in self.episodic_memory.retrieve(first_n=first_n, last_n=last_n, include_omission_info=include_omission_info):
        try:
            if not (skip_system and message[&#39;role&#39;] == &#39;system&#39;):
                msg_simplified_type = &#34;&#34;
                msg_simplified_content = &#34;&#34;
                msg_simplified_actor = &#34;&#34;

                lines.append(self._pretty_timestamp(message[&#39;role&#39;], message[&#39;simulation_timestamp&#39;]))

                if message[&#34;role&#34;] == &#34;system&#34;:
                    msg_simplified_actor = &#34;SYSTEM&#34;
                    msg_simplified_type = message[&#34;role&#34;]
                    msg_simplified_content = message[&#34;content&#34;]

                    lines.append(
                        f&#34;[dim] {msg_simplified_type}: {msg_simplified_content}[/]&#34;
                    )

                elif message[&#34;role&#34;] == &#34;user&#34;:
                    lines.append(
                        self._pretty_stimuli(
                            role=message[&#34;role&#34;],
                            content=message[&#34;content&#34;],
                            simplified=simplified,
                            max_content_length=max_content_length,
                        )
                    )

                elif message[&#34;role&#34;] == &#34;assistant&#34;:
                    lines.append(
                        self._pretty_action(
                            role=message[&#34;role&#34;],
                            content=message[&#34;content&#34;],
                            simplified=simplified,
                            max_content_length=max_content_length,
                        )
                    )
                else:
                    lines.append(f&#34;{message[&#39;role&#39;]}: {message[&#39;content&#39;]}&#34;)
        except:
            # print(f&#34;ERROR: {message}&#34;)
            continue

      return &#34;\n&#34;.join(lines)

    def _pretty_stimuli(
        self,
        role,
        content,
        simplified=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ) -&gt; list:
        &#34;&#34;&#34;
        Pretty prints stimuli.
        &#34;&#34;&#34;

        lines = []
        msg_simplified_actor = &#34;USER&#34;
        for stimus in content[&#34;stimuli&#34;]:
            if simplified:
                if stimus[&#34;source&#34;] != &#34;&#34;:
                    msg_simplified_actor = stimus[&#34;source&#34;]

                else:
                    msg_simplified_actor = &#34;USER&#34;

                msg_simplified_type = stimus[&#34;type&#34;]
                msg_simplified_content = break_text_at_length(
                    stimus[&#34;content&#34;], max_length=max_content_length
                )

                indent = &#34; &#34; * len(msg_simplified_actor) + &#34;      &gt; &#34;
                msg_simplified_content = textwrap.fill(
                    msg_simplified_content,
                    width=TinyPerson.PP_TEXT_WIDTH,
                    initial_indent=indent,
                    subsequent_indent=indent,
                )

                #
                # Using rich for formatting. Let&#39;s make things as readable as possible!
                #
                if msg_simplified_type == &#34;CONVERSATION&#34;:
                    rich_style = &#34;bold italic cyan1&#34;
                elif msg_simplified_type == &#34;THOUGHT&#34;:
                    rich_style = &#34;dim italic cyan1&#34;
                else:
                    rich_style = &#34;italic&#34;

                lines.append(
                    f&#34;[{rich_style}][underline]{msg_simplified_actor}[/] --&gt; [{rich_style}][underline]{self.name}[/]: [{msg_simplified_type}] \n{msg_simplified_content}[/]&#34;
                )
            else:
                lines.append(f&#34;{role}: {content}&#34;)

        return &#34;\n&#34;.join(lines)

    def _pretty_action(
        self,
        role,
        content,
        simplified=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ) -&gt; str:
        &#34;&#34;&#34;
        Pretty prints an action.
        &#34;&#34;&#34;
        if simplified:
            msg_simplified_actor = self.name
            msg_simplified_type = content[&#34;action&#34;][&#34;type&#34;]
            msg_simplified_content = break_text_at_length(
                content[&#34;action&#34;].get(&#34;content&#34;, &#34;&#34;), max_length=max_content_length
            )

            indent = &#34; &#34; * len(msg_simplified_actor) + &#34;      &gt; &#34;
            msg_simplified_content = textwrap.fill(
                msg_simplified_content,
                width=TinyPerson.PP_TEXT_WIDTH,
                initial_indent=indent,
                subsequent_indent=indent,
            )

            #
            # Using rich for formatting. Let&#39;s make things as readable as possible!
            #
            if msg_simplified_type == &#34;DONE&#34;:
                rich_style = &#34;grey82&#34;
            elif msg_simplified_type == &#34;TALK&#34;:
                rich_style = &#34;bold green3&#34;
            elif msg_simplified_type == &#34;THINK&#34;:
                rich_style = &#34;green&#34;
            else:
                rich_style = &#34;purple&#34;

            return f&#34;[{rich_style}][underline]{msg_simplified_actor}[/] acts: [{msg_simplified_type}] \n{msg_simplified_content}[/]&#34;
        else:
            return f&#34;{role}: {content}&#34;
    
    def _pretty_timestamp(
        self,
        role,
        timestamp,
    ) -&gt; str:
        &#34;&#34;&#34;
        Pretty prints a timestamp.
        &#34;&#34;&#34;
        return f&#34;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; Date and time of events: {timestamp}&#34;

    def iso_datetime(self) -&gt; str:
        &#34;&#34;&#34;
        Returns the current datetime of the environment, if any.

        Returns:
            datetime: The current datetime of the environment in ISO forat.
        &#34;&#34;&#34;
        if self.environment is not None and self.environment.current_datetime is not None:
            return self.environment.current_datetime.isoformat()
        else:
            return None

    ###########################################################
    # IO
    ###########################################################

    def save_spec(self, path, include_mental_faculties=True, include_memory=False):
        &#34;&#34;&#34;
        Saves the current configuration to a JSON file.
        &#34;&#34;&#34;
        
        suppress_attributes = []

        # should we include the memory?
        if not include_memory:
            suppress_attributes.append(&#34;episodic_memory&#34;)
            suppress_attributes.append(&#34;semantic_memory&#34;)

        # should we include the mental faculties?
        if not include_mental_faculties:
            suppress_attributes.append(&#34;_mental_faculties&#34;)

        self.to_json(suppress=suppress_attributes, file_path=path)

    
    @staticmethod
    def load_spec(path, suppress_mental_faculties=False, suppress_memory=False, auto_rename_agent=False, new_agent_name=None):
        &#34;&#34;&#34;
        Loads a JSON agent specification.

        Args:
            path (str): The path to the JSON file containing the agent specification.
            suppress_mental_faculties (bool, optional): Whether to suppress loading the mental faculties. Defaults to False.
            suppress_memory (bool, optional): Whether to suppress loading the memory. Defaults to False.
        &#34;&#34;&#34;

        suppress_attributes = []

        # should we suppress the mental faculties?
        if suppress_mental_faculties:
            suppress_attributes.append(&#34;_mental_faculties&#34;)

        # should we suppress the memory?
        if suppress_memory:
            suppress_attributes.append(&#34;episodic_memory&#34;)
            suppress_attributes.append(&#34;semantic_memory&#34;)

        return TinyPerson.from_json(json_dict_or_path=path, suppress=suppress_attributes, 
                                    post_init_params={&#34;auto_rename_agent&#34;: auto_rename_agent, &#34;new_agent_name&#34;: new_agent_name})


    def encode_complete_state(self) -&gt; dict:
        &#34;&#34;&#34;
        Encodes the complete state of the TinyPerson, including the current messages, accessible agents, etc.
        This is meant for serialization and caching purposes, not for exporting the state to the user.
        &#34;&#34;&#34;
        to_copy = copy.copy(self.__dict__)

        # delete the logger and other attributes that cannot be serialized
        del to_copy[&#34;environment&#34;]
        del to_copy[&#34;_mental_faculties&#34;]

        to_copy[&#34;_accessible_agents&#34;] = [agent.name for agent in self._accessible_agents]
        to_copy[&#39;episodic_memory&#39;] = self.episodic_memory.to_json()
        to_copy[&#39;semantic_memory&#39;] = self.semantic_memory.to_json()
        to_copy[&#34;_mental_faculties&#34;] = [faculty.to_json() for faculty in self._mental_faculties]

        state = copy.deepcopy(to_copy)

        return state

    def decode_complete_state(self, state: dict) -&gt; Self:
        &#34;&#34;&#34;
        Loads the complete state of the TinyPerson, including the current messages,
        and produces a new TinyPerson instance.
        &#34;&#34;&#34;
        state = copy.deepcopy(state)
        
        self._accessible_agents = [TinyPerson.get_agent_by_name(name) for name in state[&#34;_accessible_agents&#34;]]
        self.episodic_memory = EpisodicMemory.from_json(state[&#39;episodic_memory&#39;])
        self.semantic_memory = SemanticMemory.from_json(state[&#39;semantic_memory&#39;])
        
        for i, faculty in enumerate(self._mental_faculties):
            faculty = faculty.from_json(state[&#39;_mental_faculties&#39;][i])

        # delete fields already present in the state
        del state[&#34;_accessible_agents&#34;]
        del state[&#39;episodic_memory&#39;]
        del state[&#39;semantic_memory&#39;]
        del state[&#39;_mental_faculties&#39;]

        # restore other fields
        self.__dict__.update(state)


        return self
    
    def create_new_agent_from_current_spec(self, new_name:str) -&gt; Self:
        &#34;&#34;&#34;
        Creates a new agent from the current agent&#39;s specification. 

        Args:
            new_name (str): The name of the new agent. Agent names must be unique in the simulation, 
              this is why we need to provide a new name.
        &#34;&#34;&#34;
        new_agent = TinyPerson(name=new_name, spec_path=None)
        
        new_config = copy.deepcopy(self._configuration)
        new_config[&#39;name&#39;] = new_name

        new_agent._configuration = new_config

        return new_agent
        

    @staticmethod
    def add_agent(agent):
        &#34;&#34;&#34;
        Adds an agent to the global list of agents. Agent names must be unique,
        so this method will raise an exception if the name is already in use.
        &#34;&#34;&#34;
        if agent.name in TinyPerson.all_agents:
            raise ValueError(f&#34;Agent name {agent.name} is already in use.&#34;)
        else:
            TinyPerson.all_agents[agent.name] = agent

    @staticmethod
    def has_agent(agent_name: str):
        &#34;&#34;&#34;
        Checks if an agent is already registered.
        &#34;&#34;&#34;
        return agent_name in TinyPerson.all_agents

    @staticmethod
    def set_simulation_for_free_agents(simulation):
        &#34;&#34;&#34;
        Sets the simulation if it is None. This allows free agents to be captured by specific simulation scopes
        if desired.
        &#34;&#34;&#34;
        for agent in TinyPerson.all_agents.values():
            if agent.simulation_id is None:
                simulation.add_agent(agent)

    @staticmethod
    def get_agent_by_name(name):
        &#34;&#34;&#34;
        Gets an agent by name.
        &#34;&#34;&#34;
        if name in TinyPerson.all_agents:
            return TinyPerson.all_agents[name]
        else:
            return None

    @staticmethod
    def clear_agents():
        &#34;&#34;&#34;
        Clears the global list of agents.
        &#34;&#34;&#34;
        TinyPerson.all_agents = {}        



#######################################################################################################################
# Mental faculties
#######################################################################################################################
    
class TinyMentalFaculty(JsonSerializableRegistry):
    &#34;&#34;&#34;
    Represents a mental faculty of an agent. Mental faculties are the cognitive abilities that an agent has.
    &#34;&#34;&#34;

    def __init__(self, name: str, requires_faculties: list=None) -&gt; None:
        &#34;&#34;&#34;
        Initializes the mental faculty.

        Args:
            name (str): The name of the mental faculty.
            requires_faculties (list): A list of mental faculties that this faculty requires to function properly.
        &#34;&#34;&#34;
        self.name = name
        
        if requires_faculties is None:
            self.requires_faculties = []
        else:
            self.requires_faculties = requires_faculties

    def __str__(self) -&gt; str:
        return f&#34;Mental Faculty: {self.name}&#34;
    
    def __eq__(self, other):
        if isinstance(other, TinyMentalFaculty):
            return self.name == other.name
        return False
    
    def process_action(self, agent, action: dict) -&gt; bool:
        &#34;&#34;&#34;
        Processes an action related to this faculty.

        Args:
            action (dict): The action to process.
        
        Returns:
            bool: True if the action was successfully processed, False otherwise.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)
    
    def actions_definitions_prompt(self) -&gt; str:
        &#34;&#34;&#34;
        Returns the prompt for defining a actions related to this faculty.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def actions_constraints_prompt(self) -&gt; str:
        &#34;&#34;&#34;
        Returns the prompt for defining constraints on actions related to this faculty.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)


class RecallFaculty(TinyMentalFaculty):

    def __init__(self):
        super().__init__(&#34;Memory Recall&#34;)
        

    def process_action(self, agent, action: dict) -&gt; bool:
        if action[&#39;type&#39;] == &#34;RECALL&#34; and action[&#39;content&#39;] is not None:
            content = action[&#39;content&#39;]

            semantic_memories = agent.semantic_memory.retrieve_relevant(relevance_target=content)

            if len(semantic_memories) &gt; 0:
                # a string with each element in the list in a new line starting with a bullet point
                agent.think(&#34;I have remembered the following information from my semantic memory and will use it to guide me in my subsequent actions: \n&#34; + \
                        &#34;\n&#34;.join([f&#34;  - {item}&#34; for item in semantic_memories]))
            else:
                agent.think(f&#34;I can&#39;t remember anything about &#39;{content}&#39;.&#34;)
            
            return True
        
        else:
            return False

    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
              - RECALL: you can recall information from your memory. To do, you must specify a &#34;mental query&#34; to locate the desired memory. If the memory is found, it is brought to your conscience.
            &#34;&#34;&#34;

        return textwrap.dedent(prompt)
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
          &#34;&#34;&#34;
            - You try to RECALL information from your semantic/factual memory, so that you can have more relevant elements to think and talk about, whenever such an action would be likely
                to enrich the current interaction. To do so, you must specify able &#34;mental query&#34; that is related to the things you&#39;ve been thinking, listening and talking about.
                Example:
                ```
                &lt;THINK A&gt;
                &lt;RECALL B, which is something related to A&gt;
                &lt;THINK about A and B&gt;
                &lt;TALK about A and B&gt;
                DONE
                ```
            - If you RECALL:
                * you use a &#34;mental query&#34; that describe the elements you are looking for, you do not use a question. It is like a keyword-based search query.
                For example, instead of &#34;What are the symptoms of COVID-19?&#34;, you would use &#34;COVID-19 symptoms&#34;.
                * you use keywords likely to be found in the text you are looking for. For example, instead of &#34;Brazil economic outlook&#34;, you would use &#34;Brazil economy&#34;, &#34;Brazil GPD&#34;, &#34;Brazil inflation&#34;, etc.
            - It may take several tries of RECALL to get the relevant information you need. If you don&#39;t find what you are looking for, you can try again with a **very** different &#34;mental query&#34;.
                Be creative: you can use synonyms, related concepts, or any other strategy you think might help you to find the information you need. Avoid using the same terms in different queries, as it is likely to return the same results. Whenever necessary, you should retry RECALL a couple of times before giving up the location of more information.
                Example:
                ```
                &lt;THINK something&gt;
                &lt;RECALL &#34;cat products&#34;&gt;
                &lt;THINK something&gt;
                &lt;RECALL &#34;feline artifacts&#34;&gt;
                &lt;THINK something&gt;
                &lt;RECALL &#34;pet store&#34;&gt;
                &lt;THINK something&gt;
                &lt;TALK something&gt;
                DONE
                ```
            - You **may** interleave THINK and RECALL so that you can better reflect on the information you are trying to recall.
            - If you need information about a specific document, you **must** use CONSULT instead of RECALL. This is because RECALL **does not** allow you to select the specific document, and only brings small 
                relevant parts of variious documents - while CONSULT brings the precise document requested for your inspection, with its full content. 
                Example:
                ```
                LIST_DOCUMENTS
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                DONE
                ``` 
          &#34;&#34;&#34;

        return textwrap.dedent(prompt)
    

class FilesAndWebGroundingFaculty(TinyMentalFaculty):
    &#34;&#34;&#34;
    Allows the agent to access local files and web pages to ground its knowledge.
    &#34;&#34;&#34;


    def __init__(self):
        super().__init__(&#34;Local Grounding&#34;)

    def process_action(self, agent, action: dict) -&gt; bool:
        if action[&#39;type&#39;] == &#34;CONSULT&#34; and action[&#39;content&#39;] is not None:
            content = action[&#39;content&#39;]

            document_content = agent.semantic_memory.retrieve_document_content_by_name(document_name=content)

            if document_content is not None:
                agent.think(f&#34;I have read the following document: \n{document_content}&#34;)
            else:
                agent.think(f&#34;I can&#39;t find any document with the name &#39;{content}&#39;.&#34;)
            
            return True
        
        elif action[&#39;type&#39;] == &#34;LIST_DOCUMENTS&#34; and action[&#39;content&#39;] is not None:
            documents_names = self.semantic_memory.list_documents_names()

            if len(documents_names) &gt; 0:
                agent.think(f&#34;I have the following documents available to me: {documents_names}&#34;)
            else:
                agent.think(f&#34;I don&#39;t have any documents available for inspection.&#34;)
            
            return True

        else:
            return False


    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
            - LIST_DOCUMENTS: you can list the documents you have access to, so that you can decide which to access, if any, to accomplish your goals. Documents is a generic term and includes any 
                kind of  &#34;packaged&#34; information you can access, such as emails, files, chat messages, calendar events, etc.
            - CONSULT: you can retrieve and consult a specific document, so that you can access its content and accomplish your goals. To do so, you specify the name of the document you want to consult.
            &#34;&#34;&#34;

        return textwrap.dedent(prompt)
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
          &#34;&#34;&#34;
            - If you need information about a specific document, you **must** use CONSULT instead of RECALL. This is because RECALL **does not** allow you to select the specific document, and only brings small 
                relevant parts of variious documents - while CONSULT brings the precise document requested for your inspection, with its full content. 
                Example:
                ```
                LIST_DOCUMENTS
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                DONE
                ``` 
            - If you need information from specific documents, you **always** CONSULT it, **never** RECALL it.   
            - You can only CONSULT few documents before issuing DONE. 
                Example:
                ```
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                DONE
                ```
            - When deciding whether to use RECALL or CONSULT, you should consider whether you are looking for any information about some topic (use RECALL) or if you are looking for information from
                specific documents (use CONSULT). To know if you have potentially relevant documents available, use LIST_DOCUMENTS first.
          &#34;&#34;&#34;

        return textwrap.dedent(prompt)
    
class TinyToolUse(TinyMentalFaculty):
    &#34;&#34;&#34;
    Allows the agent to use tools to accomplish tasks. Tool usage is one of the most important cognitive skills
    humans and primates have as we know.
    &#34;&#34;&#34;

    def __init__(self, tools:list) -&gt; None:
        super().__init__(&#34;Tool Use&#34;)
    
        self.tools = tools
    
    def process_action(self, agent, action: dict) -&gt; bool:
        for tool in self.tools:
            if tool.process_action(agent, action):
                return True
        
        return False
    
    def actions_definitions_prompt(self) -&gt; str:
        # each tool should provide its own actions definitions prompt
        prompt = &#34;&#34;
        for tool in self.tools:
            prompt += tool.actions_definitions_prompt()
        
        return prompt
    
    def actions_constraints_prompt(self) -&gt; str:
        # each tool should provide its own actions constraints prompt
        prompt = &#34;&#34;
        for tool in self.tools:
            prompt += tool.actions_constraints_prompt()
        
        return prompt


#######################################################################################################################
# Memory mechanisms 
#######################################################################################################################

class TinyMemory(TinyMentalFaculty):
    &#34;&#34;&#34;
    Base class for different types of memory.
    &#34;&#34;&#34;

    def store(self, value: Any) -&gt; None:
        &#34;&#34;&#34;
        Stores a value in memory.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve(self, first_n: int, last_n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the first n and/or last n values from memory. If n is None, all values are retrieved.

        Args:
            first_n (int): The number of first values to retrieve.
            last_n (int): The number of last values to retrieve.
            include_omission_info (bool): Whether to include an information message when some values are omitted.

        Returns:
            list: The retrieved values.
        
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_recent(self) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the n most recent values from memory.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_all(self) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_relevant(self, relevance_target:str, top_k=5) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory that are relevant to a given target.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)



class EpisodicMemory(TinyMemory):
    &#34;&#34;&#34;
    Provides episodic memory capabilities to an agent. Cognitively, episodic memory is the ability to remember specific events,
    or episodes, in the past. This class provides a simple implementation of episodic memory, where the agent can store and retrieve
    messages from memory.
    
    Subclasses of this class can be used to provide different memory implementations.
    &#34;&#34;&#34;

    MEMORY_BLOCK_OMISSION_INFO = {&#39;role&#39;: &#39;assistant&#39;, &#39;content&#39;: &#34;Info: there were other messages here, but they were omitted for brevity.&#34;, &#39;simulation_timestamp&#39;: None}

    def __init__(
        self, fixed_prefix_length: int = 100, lookback_length: int = 100
    ) -&gt; None:
        &#34;&#34;&#34;
        Initializes the memory.

        Args:
            fixed_prefix_length (int): The fixed prefix length. Defaults to 20.
            lookback_length (int): The lookback length. Defaults to 20.
        &#34;&#34;&#34;
        self.fixed_prefix_length = fixed_prefix_length
        self.lookback_length = lookback_length

        self.memory = []

    def store(self, value: Any) -&gt; None:
        &#34;&#34;&#34;
        Stores a value in memory.
        &#34;&#34;&#34;
        self.memory.append(value)

    def count(self) -&gt; int:
        &#34;&#34;&#34;
        Returns the number of values in memory.
        &#34;&#34;&#34;
        return len(self.memory)

    def retrieve(self, first_n: int, last_n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the first n and/or last n values from memory. If n is None, all values are retrieved.

        Args:
            first_n (int): The number of first values to retrieve.
            last_n (int): The number of last values to retrieve.
            include_omission_info (bool): Whether to include an information message when some values are omitted.

        Returns:
            list: The retrieved values.
        
        &#34;&#34;&#34;

        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []

        # use the other methods in the class to implement
        if first_n is not None and last_n is not None:
            return self.retrieve_first(first_n) + omisssion_info + self.retrieve_last(last_n)
        elif first_n is not None:
            return self.retrieve_first(first_n)
        elif last_n is not None:
            return self.retrieve_last(last_n)
        else:
            return self.retrieve_all()

    def retrieve_recent(self, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the n most recent values from memory.
        &#34;&#34;&#34;
        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []

        # compute fixed prefix
        fixed_prefix = self.memory[: self.fixed_prefix_length] + omisssion_info

        # how many lookback values remain?
        remaining_lookback = min(
            len(self.memory) - len(fixed_prefix), self.lookback_length
        )

        # compute the remaining lookback values and return the concatenation
        if remaining_lookback &lt;= 0:
            return fixed_prefix
        else:
            return fixed_prefix + self.memory[-remaining_lookback:]

    def retrieve_all(self) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory.
        &#34;&#34;&#34;
        return copy.copy(self.memory)

    def retrieve_relevant(self, relevance_target: str) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory that are relevant to a given target.
        &#34;&#34;&#34;
        # TODO
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_first(self, n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the first n values from memory.
        &#34;&#34;&#34;
        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []
        
        return self.memory[:n] + omisssion_info
    
    def retrieve_last(self, n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the last n values from memory.
        &#34;&#34;&#34;
        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []

        return omisssion_info + self.memory[-n:]


class SemanticMemory(TinyMemory):
    &#34;&#34;&#34;
    Semantic memory is the memory of meanings, understandings, and other concept-based knowledge unrelated to specific experiences.
    It is not ordered temporally, and it is not about remembering specific events or episodes. This class provides a simple implementation
    of semantic memory, where the agent can store and retrieve semantic information.
    &#34;&#34;&#34;

    suppress_attributes_from_serialization = [&#34;index&#34;]

    def __init__(self, documents_paths: list=None, web_urls: list=None) -&gt; None:
        self.index = None
        
        self.documents_paths = []
        self.documents_web_urls = []

        self.documents = []
        self.filename_to_document = {}

        # load document paths and web urls
        self.add_documents_paths(documents_paths)
        
        if web_urls is not None:
            self.add_web_urls(web_urls)
    
    def retrieve_relevant(self, relevance_target:str, top_k=5) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory that are relevant to a given target.
        &#34;&#34;&#34;
        if self.index is not None:
            retriever = self.index.as_retriever(similarity_top_k=top_k)
            nodes = retriever.retrieve(&#34;Microsoft&#39;s recent major investments&#34;)
        else:
            nodes = []

        retrieved = []
        for node in nodes:
            content = &#34;SOURCE: &#34; + node.metadata[&#39;file_name&#39;]
            content += &#34;\n&#34; + &#34;SIMILARITY SCORE:&#34; + str(node.score)
            content += &#34;\n&#34; + &#34;RELEVANT CONTENT:&#34; + node.text
            retrieved.append(content)
        
        return retrieved
    
    def retrieve_document_content_by_name(self, document_name:str) -&gt; str:
        &#34;&#34;&#34;
        Retrieves a document by its name.
        &#34;&#34;&#34;
        if self.filename_to_document is not None:
            doc = self.filename_to_document[document_name]
            if doc is not None:
                content = &#34;SOURCE: &#34; + document_name
                content += &#34;\n&#34; + &#34;CONTENT: &#34; + doc.text[:10000] # TODO a more intelligent way to limit the content
                return content
            else:
                return None
        else:
            return None
    
    def list_documents_names(self) -&gt; list:
        &#34;&#34;&#34;
        Lists the names of the documents in memory.
        &#34;&#34;&#34;
        if self.filename_to_document is not None:
            return list(self.filename_to_document.keys())
        else:
            return []
    
    def add_documents_paths(self, documents_paths:list) -&gt; None:
        &#34;&#34;&#34;
        Adds a path to a folder with documents used for semantic memory.
        &#34;&#34;&#34;

        if documents_paths is not None:
            for documents_path in documents_paths:
                self.add_documents_path(documents_path)

    def add_documents_path(self, documents_path:str) -&gt; None:
        &#34;&#34;&#34;
        Adds a path to a folder with documents used for semantic memory.
        &#34;&#34;&#34;

        if documents_path not in self.documents_paths:
            self.documents_paths.append(documents_path)
            new_documents = SimpleDirectoryReader(documents_path).load_data()
            self._add_documents(new_documents, lambda doc: doc.metadata[&#34;file_name&#34;])
    
    def add_web_urls(self, web_urls:list) -&gt; None:
        &#34;&#34;&#34; 
        Adds the data retrieved from the specified URLs to documents used for semantic memory.
        &#34;&#34;&#34;
        filtered_web_urls = [url for url in web_urls if url not in self.documents_web_urls]
        self.documents_web_urls += filtered_web_urls

        if len(filtered_web_urls) &gt; 0:
            new_documents = SimpleWebPageReader(html_to_text=True).load_data(filtered_web_urls)
            self._add_documents(new_documents, lambda doc: doc.id_)
    
    def add_web_url(self, web_url:str) -&gt; None:
        &#34;&#34;&#34;
        Adds the data retrieved from the specified URL to documents used for semantic memory.
        &#34;&#34;&#34;
        # we do it like this because the add_web_urls could run scrapes in parallel, so it is better
        # to implement this one in terms of the other
        self.add_web_urls([web_url])

    def _add_documents(self, new_documents, doc_to_name_func) -&gt; list:
        &#34;&#34;&#34;
        Adds documents to the semantic memory.
        &#34;&#34;&#34;
        # index documents by name
        if len(new_documents) &gt; 0:
            # add the new documents to the list of documents
            self.documents += new_documents

            # process documents individually too
            for document in new_documents:
                
                # out of an abundance of caution, we sanitize the text
                document.text = utils.sanitize_raw_string(document.text)

                name = doc_to_name_func(document)
                self.filename_to_document[name] = document

            # index documents for semantic retrieval
            if self.index is None:
                self.index = VectorStoreIndex.from_documents(self.documents)
            else:
                self.index.refresh(self.documents)



    ###########################################################
    # IO
    ###########################################################

    def _post_deserialization_init(self):
        super()._post_deserialization_init()
    
        self.add_documents_paths(self.documents_paths)
        self.add_web_urls(self.documents_web_urls)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="tinytroupe.agent.EpisodicMemory"><code class="flex name class">
<span>class <span class="ident">EpisodicMemory</span></span>
<span>(</span><span>fixed_prefix_length: int = 100, lookback_length: int = 100)</span>
</code></dt>
<dd>
<div class="desc"><p>Provides episodic memory capabilities to an agent. Cognitively, episodic memory is the ability to remember specific events,
or episodes, in the past. This class provides a simple implementation of episodic memory, where the agent can store and retrieve
messages from memory.</p>
<p>Subclasses of this class can be used to provide different memory implementations.</p>
<p>Initializes the memory.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>fixed_prefix_length</code></strong> :&ensp;<code>int</code></dt>
<dd>The fixed prefix length. Defaults to 20.</dd>
<dt><strong><code>lookback_length</code></strong> :&ensp;<code>int</code></dt>
<dd>The lookback length. Defaults to 20.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class EpisodicMemory(TinyMemory):
    &#34;&#34;&#34;
    Provides episodic memory capabilities to an agent. Cognitively, episodic memory is the ability to remember specific events,
    or episodes, in the past. This class provides a simple implementation of episodic memory, where the agent can store and retrieve
    messages from memory.
    
    Subclasses of this class can be used to provide different memory implementations.
    &#34;&#34;&#34;

    MEMORY_BLOCK_OMISSION_INFO = {&#39;role&#39;: &#39;assistant&#39;, &#39;content&#39;: &#34;Info: there were other messages here, but they were omitted for brevity.&#34;, &#39;simulation_timestamp&#39;: None}

    def __init__(
        self, fixed_prefix_length: int = 100, lookback_length: int = 100
    ) -&gt; None:
        &#34;&#34;&#34;
        Initializes the memory.

        Args:
            fixed_prefix_length (int): The fixed prefix length. Defaults to 20.
            lookback_length (int): The lookback length. Defaults to 20.
        &#34;&#34;&#34;
        self.fixed_prefix_length = fixed_prefix_length
        self.lookback_length = lookback_length

        self.memory = []

    def store(self, value: Any) -&gt; None:
        &#34;&#34;&#34;
        Stores a value in memory.
        &#34;&#34;&#34;
        self.memory.append(value)

    def count(self) -&gt; int:
        &#34;&#34;&#34;
        Returns the number of values in memory.
        &#34;&#34;&#34;
        return len(self.memory)

    def retrieve(self, first_n: int, last_n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the first n and/or last n values from memory. If n is None, all values are retrieved.

        Args:
            first_n (int): The number of first values to retrieve.
            last_n (int): The number of last values to retrieve.
            include_omission_info (bool): Whether to include an information message when some values are omitted.

        Returns:
            list: The retrieved values.
        
        &#34;&#34;&#34;

        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []

        # use the other methods in the class to implement
        if first_n is not None and last_n is not None:
            return self.retrieve_first(first_n) + omisssion_info + self.retrieve_last(last_n)
        elif first_n is not None:
            return self.retrieve_first(first_n)
        elif last_n is not None:
            return self.retrieve_last(last_n)
        else:
            return self.retrieve_all()

    def retrieve_recent(self, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the n most recent values from memory.
        &#34;&#34;&#34;
        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []

        # compute fixed prefix
        fixed_prefix = self.memory[: self.fixed_prefix_length] + omisssion_info

        # how many lookback values remain?
        remaining_lookback = min(
            len(self.memory) - len(fixed_prefix), self.lookback_length
        )

        # compute the remaining lookback values and return the concatenation
        if remaining_lookback &lt;= 0:
            return fixed_prefix
        else:
            return fixed_prefix + self.memory[-remaining_lookback:]

    def retrieve_all(self) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory.
        &#34;&#34;&#34;
        return copy.copy(self.memory)

    def retrieve_relevant(self, relevance_target: str) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory that are relevant to a given target.
        &#34;&#34;&#34;
        # TODO
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_first(self, n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the first n values from memory.
        &#34;&#34;&#34;
        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []
        
        return self.memory[:n] + omisssion_info
    
    def retrieve_last(self, n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the last n values from memory.
        &#34;&#34;&#34;
        omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []

        return omisssion_info + self.memory[-n:]</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.TinyMemory" href="#tinytroupe.agent.TinyMemory">TinyMemory</a></li>
<li><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="tinytroupe.agent.EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO"><code class="name">var <span class="ident">MEMORY_BLOCK_OMISSION_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.agent.EpisodicMemory.count"><code class="name flex">
<span>def <span class="ident">count</span></span>(<span>self) ‑> int</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the number of values in memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def count(self) -&gt; int:
    &#34;&#34;&#34;
    Returns the number of values in memory.
    &#34;&#34;&#34;
    return len(self.memory)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.EpisodicMemory.retrieve_first"><code class="name flex">
<span>def <span class="ident">retrieve_first</span></span>(<span>self, n: int, include_omission_info: bool = True) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves the first n values from memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def retrieve_first(self, n: int, include_omission_info:bool=True) -&gt; list:
    &#34;&#34;&#34;
    Retrieves the first n values from memory.
    &#34;&#34;&#34;
    omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []
    
    return self.memory[:n] + omisssion_info</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.EpisodicMemory.retrieve_last"><code class="name flex">
<span>def <span class="ident">retrieve_last</span></span>(<span>self, n: int, include_omission_info: bool = True) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves the last n values from memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def retrieve_last(self, n: int, include_omission_info:bool=True) -&gt; list:
    &#34;&#34;&#34;
    Retrieves the last n values from memory.
    &#34;&#34;&#34;
    omisssion_info = [EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO] if include_omission_info else []

    return omisssion_info + self.memory[-n:]</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.agent.TinyMemory" href="#tinytroupe.agent.TinyMemory">TinyMemory</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.agent.TinyMemory.actions_constraints_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.actions_definitions_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.process_action" href="#tinytroupe.agent.TinyMentalFaculty.process_action">process_action</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve" href="#tinytroupe.agent.TinyMemory.retrieve">retrieve</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_all" href="#tinytroupe.agent.TinyMemory.retrieve_all">retrieve_all</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_recent" href="#tinytroupe.agent.TinyMemory.retrieve_recent">retrieve_recent</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_relevant" href="#tinytroupe.agent.TinyMemory.retrieve_relevant">retrieve_relevant</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.store" href="#tinytroupe.agent.TinyMemory.store">store</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.agent.FilesAndWebGroundingFaculty"><code class="flex name class">
<span>class <span class="ident">FilesAndWebGroundingFaculty</span></span>
</code></dt>
<dd>
<div class="desc"><p>Allows the agent to access local files and web pages to ground its knowledge.</p>
<p>Initializes the mental faculty.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the mental faculty.</dd>
<dt><strong><code>requires_faculties</code></strong> :&ensp;<code>list</code></dt>
<dd>A list of mental faculties that this faculty requires to function properly.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class FilesAndWebGroundingFaculty(TinyMentalFaculty):
    &#34;&#34;&#34;
    Allows the agent to access local files and web pages to ground its knowledge.
    &#34;&#34;&#34;


    def __init__(self):
        super().__init__(&#34;Local Grounding&#34;)

    def process_action(self, agent, action: dict) -&gt; bool:
        if action[&#39;type&#39;] == &#34;CONSULT&#34; and action[&#39;content&#39;] is not None:
            content = action[&#39;content&#39;]

            document_content = agent.semantic_memory.retrieve_document_content_by_name(document_name=content)

            if document_content is not None:
                agent.think(f&#34;I have read the following document: \n{document_content}&#34;)
            else:
                agent.think(f&#34;I can&#39;t find any document with the name &#39;{content}&#39;.&#34;)
            
            return True
        
        elif action[&#39;type&#39;] == &#34;LIST_DOCUMENTS&#34; and action[&#39;content&#39;] is not None:
            documents_names = self.semantic_memory.list_documents_names()

            if len(documents_names) &gt; 0:
                agent.think(f&#34;I have the following documents available to me: {documents_names}&#34;)
            else:
                agent.think(f&#34;I don&#39;t have any documents available for inspection.&#34;)
            
            return True

        else:
            return False


    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
            - LIST_DOCUMENTS: you can list the documents you have access to, so that you can decide which to access, if any, to accomplish your goals. Documents is a generic term and includes any 
                kind of  &#34;packaged&#34; information you can access, such as emails, files, chat messages, calendar events, etc.
            - CONSULT: you can retrieve and consult a specific document, so that you can access its content and accomplish your goals. To do so, you specify the name of the document you want to consult.
            &#34;&#34;&#34;

        return textwrap.dedent(prompt)
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
          &#34;&#34;&#34;
            - If you need information about a specific document, you **must** use CONSULT instead of RECALL. This is because RECALL **does not** allow you to select the specific document, and only brings small 
                relevant parts of variious documents - while CONSULT brings the precise document requested for your inspection, with its full content. 
                Example:
                ```
                LIST_DOCUMENTS
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                DONE
                ``` 
            - If you need information from specific documents, you **always** CONSULT it, **never** RECALL it.   
            - You can only CONSULT few documents before issuing DONE. 
                Example:
                ```
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                DONE
                ```
            - When deciding whether to use RECALL or CONSULT, you should consider whether you are looking for any information about some topic (use RECALL) or if you are looking for information from
                specific documents (use CONSULT). To know if you have potentially relevant documents available, use LIST_DOCUMENTS first.
          &#34;&#34;&#34;

        return textwrap.dedent(prompt)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.process_action" href="#tinytroupe.agent.TinyMentalFaculty.process_action">process_action</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.agent.RecallFaculty"><code class="flex name class">
<span>class <span class="ident">RecallFaculty</span></span>
</code></dt>
<dd>
<div class="desc"><p>Represents a mental faculty of an agent. Mental faculties are the cognitive abilities that an agent has.</p>
<p>Initializes the mental faculty.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the mental faculty.</dd>
<dt><strong><code>requires_faculties</code></strong> :&ensp;<code>list</code></dt>
<dd>A list of mental faculties that this faculty requires to function properly.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class RecallFaculty(TinyMentalFaculty):

    def __init__(self):
        super().__init__(&#34;Memory Recall&#34;)
        

    def process_action(self, agent, action: dict) -&gt; bool:
        if action[&#39;type&#39;] == &#34;RECALL&#34; and action[&#39;content&#39;] is not None:
            content = action[&#39;content&#39;]

            semantic_memories = agent.semantic_memory.retrieve_relevant(relevance_target=content)

            if len(semantic_memories) &gt; 0:
                # a string with each element in the list in a new line starting with a bullet point
                agent.think(&#34;I have remembered the following information from my semantic memory and will use it to guide me in my subsequent actions: \n&#34; + \
                        &#34;\n&#34;.join([f&#34;  - {item}&#34; for item in semantic_memories]))
            else:
                agent.think(f&#34;I can&#39;t remember anything about &#39;{content}&#39;.&#34;)
            
            return True
        
        else:
            return False

    def actions_definitions_prompt(self) -&gt; str:
        prompt = \
            &#34;&#34;&#34;
              - RECALL: you can recall information from your memory. To do, you must specify a &#34;mental query&#34; to locate the desired memory. If the memory is found, it is brought to your conscience.
            &#34;&#34;&#34;

        return textwrap.dedent(prompt)
    
    def actions_constraints_prompt(self) -&gt; str:
        prompt = \
          &#34;&#34;&#34;
            - You try to RECALL information from your semantic/factual memory, so that you can have more relevant elements to think and talk about, whenever such an action would be likely
                to enrich the current interaction. To do so, you must specify able &#34;mental query&#34; that is related to the things you&#39;ve been thinking, listening and talking about.
                Example:
                ```
                &lt;THINK A&gt;
                &lt;RECALL B, which is something related to A&gt;
                &lt;THINK about A and B&gt;
                &lt;TALK about A and B&gt;
                DONE
                ```
            - If you RECALL:
                * you use a &#34;mental query&#34; that describe the elements you are looking for, you do not use a question. It is like a keyword-based search query.
                For example, instead of &#34;What are the symptoms of COVID-19?&#34;, you would use &#34;COVID-19 symptoms&#34;.
                * you use keywords likely to be found in the text you are looking for. For example, instead of &#34;Brazil economic outlook&#34;, you would use &#34;Brazil economy&#34;, &#34;Brazil GPD&#34;, &#34;Brazil inflation&#34;, etc.
            - It may take several tries of RECALL to get the relevant information you need. If you don&#39;t find what you are looking for, you can try again with a **very** different &#34;mental query&#34;.
                Be creative: you can use synonyms, related concepts, or any other strategy you think might help you to find the information you need. Avoid using the same terms in different queries, as it is likely to return the same results. Whenever necessary, you should retry RECALL a couple of times before giving up the location of more information.
                Example:
                ```
                &lt;THINK something&gt;
                &lt;RECALL &#34;cat products&#34;&gt;
                &lt;THINK something&gt;
                &lt;RECALL &#34;feline artifacts&#34;&gt;
                &lt;THINK something&gt;
                &lt;RECALL &#34;pet store&#34;&gt;
                &lt;THINK something&gt;
                &lt;TALK something&gt;
                DONE
                ```
            - You **may** interleave THINK and RECALL so that you can better reflect on the information you are trying to recall.
            - If you need information about a specific document, you **must** use CONSULT instead of RECALL. This is because RECALL **does not** allow you to select the specific document, and only brings small 
                relevant parts of variious documents - while CONSULT brings the precise document requested for your inspection, with its full content. 
                Example:
                ```
                LIST_DOCUMENTS
                &lt;CONSULT some document name&gt;
                &lt;THINK something about the retrieved document&gt;
                &lt;TALK something&gt;
                DONE
                ``` 
          &#34;&#34;&#34;

        return textwrap.dedent(prompt)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.process_action" href="#tinytroupe.agent.TinyMentalFaculty.process_action">process_action</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.agent.SemanticMemory"><code class="flex name class">
<span>class <span class="ident">SemanticMemory</span></span>
<span>(</span><span>documents_paths: list = None, web_urls: list = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Semantic memory is the memory of meanings, understandings, and other concept-based knowledge unrelated to specific experiences.
It is not ordered temporally, and it is not about remembering specific events or episodes. This class provides a simple implementation
of semantic memory, where the agent can store and retrieve semantic information.</p>
<p>Initializes the mental faculty.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the mental faculty.</dd>
<dt><strong><code>requires_faculties</code></strong> :&ensp;<code>list</code></dt>
<dd>A list of mental faculties that this faculty requires to function properly.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class SemanticMemory(TinyMemory):
    &#34;&#34;&#34;
    Semantic memory is the memory of meanings, understandings, and other concept-based knowledge unrelated to specific experiences.
    It is not ordered temporally, and it is not about remembering specific events or episodes. This class provides a simple implementation
    of semantic memory, where the agent can store and retrieve semantic information.
    &#34;&#34;&#34;

    suppress_attributes_from_serialization = [&#34;index&#34;]

    def __init__(self, documents_paths: list=None, web_urls: list=None) -&gt; None:
        self.index = None
        
        self.documents_paths = []
        self.documents_web_urls = []

        self.documents = []
        self.filename_to_document = {}

        # load document paths and web urls
        self.add_documents_paths(documents_paths)
        
        if web_urls is not None:
            self.add_web_urls(web_urls)
    
    def retrieve_relevant(self, relevance_target:str, top_k=5) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory that are relevant to a given target.
        &#34;&#34;&#34;
        if self.index is not None:
            retriever = self.index.as_retriever(similarity_top_k=top_k)
            nodes = retriever.retrieve(&#34;Microsoft&#39;s recent major investments&#34;)
        else:
            nodes = []

        retrieved = []
        for node in nodes:
            content = &#34;SOURCE: &#34; + node.metadata[&#39;file_name&#39;]
            content += &#34;\n&#34; + &#34;SIMILARITY SCORE:&#34; + str(node.score)
            content += &#34;\n&#34; + &#34;RELEVANT CONTENT:&#34; + node.text
            retrieved.append(content)
        
        return retrieved
    
    def retrieve_document_content_by_name(self, document_name:str) -&gt; str:
        &#34;&#34;&#34;
        Retrieves a document by its name.
        &#34;&#34;&#34;
        if self.filename_to_document is not None:
            doc = self.filename_to_document[document_name]
            if doc is not None:
                content = &#34;SOURCE: &#34; + document_name
                content += &#34;\n&#34; + &#34;CONTENT: &#34; + doc.text[:10000] # TODO a more intelligent way to limit the content
                return content
            else:
                return None
        else:
            return None
    
    def list_documents_names(self) -&gt; list:
        &#34;&#34;&#34;
        Lists the names of the documents in memory.
        &#34;&#34;&#34;
        if self.filename_to_document is not None:
            return list(self.filename_to_document.keys())
        else:
            return []
    
    def add_documents_paths(self, documents_paths:list) -&gt; None:
        &#34;&#34;&#34;
        Adds a path to a folder with documents used for semantic memory.
        &#34;&#34;&#34;

        if documents_paths is not None:
            for documents_path in documents_paths:
                self.add_documents_path(documents_path)

    def add_documents_path(self, documents_path:str) -&gt; None:
        &#34;&#34;&#34;
        Adds a path to a folder with documents used for semantic memory.
        &#34;&#34;&#34;

        if documents_path not in self.documents_paths:
            self.documents_paths.append(documents_path)
            new_documents = SimpleDirectoryReader(documents_path).load_data()
            self._add_documents(new_documents, lambda doc: doc.metadata[&#34;file_name&#34;])
    
    def add_web_urls(self, web_urls:list) -&gt; None:
        &#34;&#34;&#34; 
        Adds the data retrieved from the specified URLs to documents used for semantic memory.
        &#34;&#34;&#34;
        filtered_web_urls = [url for url in web_urls if url not in self.documents_web_urls]
        self.documents_web_urls += filtered_web_urls

        if len(filtered_web_urls) &gt; 0:
            new_documents = SimpleWebPageReader(html_to_text=True).load_data(filtered_web_urls)
            self._add_documents(new_documents, lambda doc: doc.id_)
    
    def add_web_url(self, web_url:str) -&gt; None:
        &#34;&#34;&#34;
        Adds the data retrieved from the specified URL to documents used for semantic memory.
        &#34;&#34;&#34;
        # we do it like this because the add_web_urls could run scrapes in parallel, so it is better
        # to implement this one in terms of the other
        self.add_web_urls([web_url])

    def _add_documents(self, new_documents, doc_to_name_func) -&gt; list:
        &#34;&#34;&#34;
        Adds documents to the semantic memory.
        &#34;&#34;&#34;
        # index documents by name
        if len(new_documents) &gt; 0:
            # add the new documents to the list of documents
            self.documents += new_documents

            # process documents individually too
            for document in new_documents:
                
                # out of an abundance of caution, we sanitize the text
                document.text = utils.sanitize_raw_string(document.text)

                name = doc_to_name_func(document)
                self.filename_to_document[name] = document

            # index documents for semantic retrieval
            if self.index is None:
                self.index = VectorStoreIndex.from_documents(self.documents)
            else:
                self.index.refresh(self.documents)



    ###########################################################
    # IO
    ###########################################################

    def _post_deserialization_init(self):
        super()._post_deserialization_init()
    
        self.add_documents_paths(self.documents_paths)
        self.add_web_urls(self.documents_web_urls)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.TinyMemory" href="#tinytroupe.agent.TinyMemory">TinyMemory</a></li>
<li><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="tinytroupe.agent.SemanticMemory.suppress_attributes_from_serialization"><code class="name">var <span class="ident">suppress_attributes_from_serialization</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.agent.SemanticMemory.add_documents_path"><code class="name flex">
<span>def <span class="ident">add_documents_path</span></span>(<span>self, documents_path: str) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a path to a folder with documents used for semantic memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_documents_path(self, documents_path:str) -&gt; None:
    &#34;&#34;&#34;
    Adds a path to a folder with documents used for semantic memory.
    &#34;&#34;&#34;

    if documents_path not in self.documents_paths:
        self.documents_paths.append(documents_path)
        new_documents = SimpleDirectoryReader(documents_path).load_data()
        self._add_documents(new_documents, lambda doc: doc.metadata[&#34;file_name&#34;])</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.SemanticMemory.add_documents_paths"><code class="name flex">
<span>def <span class="ident">add_documents_paths</span></span>(<span>self, documents_paths: list) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a path to a folder with documents used for semantic memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_documents_paths(self, documents_paths:list) -&gt; None:
    &#34;&#34;&#34;
    Adds a path to a folder with documents used for semantic memory.
    &#34;&#34;&#34;

    if documents_paths is not None:
        for documents_path in documents_paths:
            self.add_documents_path(documents_path)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.SemanticMemory.add_web_url"><code class="name flex">
<span>def <span class="ident">add_web_url</span></span>(<span>self, web_url: str) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the data retrieved from the specified URL to documents used for semantic memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_web_url(self, web_url:str) -&gt; None:
    &#34;&#34;&#34;
    Adds the data retrieved from the specified URL to documents used for semantic memory.
    &#34;&#34;&#34;
    # we do it like this because the add_web_urls could run scrapes in parallel, so it is better
    # to implement this one in terms of the other
    self.add_web_urls([web_url])</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.SemanticMemory.add_web_urls"><code class="name flex">
<span>def <span class="ident">add_web_urls</span></span>(<span>self, web_urls: list) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the data retrieved from the specified URLs to documents used for semantic memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_web_urls(self, web_urls:list) -&gt; None:
    &#34;&#34;&#34; 
    Adds the data retrieved from the specified URLs to documents used for semantic memory.
    &#34;&#34;&#34;
    filtered_web_urls = [url for url in web_urls if url not in self.documents_web_urls]
    self.documents_web_urls += filtered_web_urls

    if len(filtered_web_urls) &gt; 0:
        new_documents = SimpleWebPageReader(html_to_text=True).load_data(filtered_web_urls)
        self._add_documents(new_documents, lambda doc: doc.id_)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.SemanticMemory.list_documents_names"><code class="name flex">
<span>def <span class="ident">list_documents_names</span></span>(<span>self) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Lists the names of the documents in memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def list_documents_names(self) -&gt; list:
    &#34;&#34;&#34;
    Lists the names of the documents in memory.
    &#34;&#34;&#34;
    if self.filename_to_document is not None:
        return list(self.filename_to_document.keys())
    else:
        return []</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.SemanticMemory.retrieve_document_content_by_name"><code class="name flex">
<span>def <span class="ident">retrieve_document_content_by_name</span></span>(<span>self, document_name: str) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves a document by its name.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def retrieve_document_content_by_name(self, document_name:str) -&gt; str:
    &#34;&#34;&#34;
    Retrieves a document by its name.
    &#34;&#34;&#34;
    if self.filename_to_document is not None:
        doc = self.filename_to_document[document_name]
        if doc is not None:
            content = &#34;SOURCE: &#34; + document_name
            content += &#34;\n&#34; + &#34;CONTENT: &#34; + doc.text[:10000] # TODO a more intelligent way to limit the content
            return content
        else:
            return None
    else:
        return None</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.agent.TinyMemory" href="#tinytroupe.agent.TinyMemory">TinyMemory</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.agent.TinyMemory.actions_constraints_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.actions_definitions_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.process_action" href="#tinytroupe.agent.TinyMentalFaculty.process_action">process_action</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve" href="#tinytroupe.agent.TinyMemory.retrieve">retrieve</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_all" href="#tinytroupe.agent.TinyMemory.retrieve_all">retrieve_all</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_recent" href="#tinytroupe.agent.TinyMemory.retrieve_recent">retrieve_recent</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_relevant" href="#tinytroupe.agent.TinyMemory.retrieve_relevant">retrieve_relevant</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.store" href="#tinytroupe.agent.TinyMemory.store">store</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.agent.TinyMemory"><code class="flex name class">
<span>class <span class="ident">TinyMemory</span></span>
<span>(</span><span>name: str, requires_faculties: list = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Base class for different types of memory.</p>
<p>Initializes the mental faculty.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the mental faculty.</dd>
<dt><strong><code>requires_faculties</code></strong> :&ensp;<code>list</code></dt>
<dd>A list of mental faculties that this faculty requires to function properly.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyMemory(TinyMentalFaculty):
    &#34;&#34;&#34;
    Base class for different types of memory.
    &#34;&#34;&#34;

    def store(self, value: Any) -&gt; None:
        &#34;&#34;&#34;
        Stores a value in memory.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve(self, first_n: int, last_n: int, include_omission_info:bool=True) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the first n and/or last n values from memory. If n is None, all values are retrieved.

        Args:
            first_n (int): The number of first values to retrieve.
            last_n (int): The number of last values to retrieve.
            include_omission_info (bool): Whether to include an information message when some values are omitted.

        Returns:
            list: The retrieved values.
        
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_recent(self) -&gt; list:
        &#34;&#34;&#34;
        Retrieves the n most recent values from memory.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_all(self) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def retrieve_relevant(self, relevance_target:str, top_k=5) -&gt; list:
        &#34;&#34;&#34;
        Retrieves all values from memory that are relevant to a given target.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.EpisodicMemory" href="#tinytroupe.agent.EpisodicMemory">EpisodicMemory</a></li>
<li><a title="tinytroupe.agent.SemanticMemory" href="#tinytroupe.agent.SemanticMemory">SemanticMemory</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.agent.TinyMemory.retrieve"><code class="name flex">
<span>def <span class="ident">retrieve</span></span>(<span>self, first_n: int, last_n: int, include_omission_info: bool = True) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves the first n and/or last n values from memory. If n is None, all values are retrieved.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>first_n</code></strong> :&ensp;<code>int</code></dt>
<dd>The number of first values to retrieve.</dd>
<dt><strong><code>last_n</code></strong> :&ensp;<code>int</code></dt>
<dd>The number of last values to retrieve.</dd>
<dt><strong><code>include_omission_info</code></strong> :&ensp;<code>bool</code></dt>
<dd>Whether to include an information message when some values are omitted.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>list</code></dt>
<dd>The retrieved values.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def retrieve(self, first_n: int, last_n: int, include_omission_info:bool=True) -&gt; list:
    &#34;&#34;&#34;
    Retrieves the first n and/or last n values from memory. If n is None, all values are retrieved.

    Args:
        first_n (int): The number of first values to retrieve.
        last_n (int): The number of last values to retrieve.
        include_omission_info (bool): Whether to include an information message when some values are omitted.

    Returns:
        list: The retrieved values.
    
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyMemory.retrieve_all"><code class="name flex">
<span>def <span class="ident">retrieve_all</span></span>(<span>self) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves all values from memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def retrieve_all(self) -&gt; list:
    &#34;&#34;&#34;
    Retrieves all values from memory.
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyMemory.retrieve_recent"><code class="name flex">
<span>def <span class="ident">retrieve_recent</span></span>(<span>self) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves the n most recent values from memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def retrieve_recent(self) -&gt; list:
    &#34;&#34;&#34;
    Retrieves the n most recent values from memory.
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyMemory.retrieve_relevant"><code class="name flex">
<span>def <span class="ident">retrieve_relevant</span></span>(<span>self, relevance_target: str, top_k=5) ‑> list</span>
</code></dt>
<dd>
<div class="desc"><p>Retrieves all values from memory that are relevant to a given target.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def retrieve_relevant(self, relevance_target:str, top_k=5) -&gt; list:
    &#34;&#34;&#34;
    Retrieves all values from memory that are relevant to a given target.
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyMemory.store"><code class="name flex">
<span>def <span class="ident">store</span></span>(<span>self, value: Any) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Stores a value in memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def store(self, value: Any) -&gt; None:
    &#34;&#34;&#34;
    Stores a value in memory.
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.process_action" href="#tinytroupe.agent.TinyMentalFaculty.process_action">process_action</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.agent.TinyMentalFaculty"><code class="flex name class">
<span>class <span class="ident">TinyMentalFaculty</span></span>
<span>(</span><span>name: str, requires_faculties: list = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Represents a mental faculty of an agent. Mental faculties are the cognitive abilities that an agent has.</p>
<p>Initializes the mental faculty.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the mental faculty.</dd>
<dt><strong><code>requires_faculties</code></strong> :&ensp;<code>list</code></dt>
<dd>A list of mental faculties that this faculty requires to function properly.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyMentalFaculty(JsonSerializableRegistry):
    &#34;&#34;&#34;
    Represents a mental faculty of an agent. Mental faculties are the cognitive abilities that an agent has.
    &#34;&#34;&#34;

    def __init__(self, name: str, requires_faculties: list=None) -&gt; None:
        &#34;&#34;&#34;
        Initializes the mental faculty.

        Args:
            name (str): The name of the mental faculty.
            requires_faculties (list): A list of mental faculties that this faculty requires to function properly.
        &#34;&#34;&#34;
        self.name = name
        
        if requires_faculties is None:
            self.requires_faculties = []
        else:
            self.requires_faculties = requires_faculties

    def __str__(self) -&gt; str:
        return f&#34;Mental Faculty: {self.name}&#34;
    
    def __eq__(self, other):
        if isinstance(other, TinyMentalFaculty):
            return self.name == other.name
        return False
    
    def process_action(self, agent, action: dict) -&gt; bool:
        &#34;&#34;&#34;
        Processes an action related to this faculty.

        Args:
            action (dict): The action to process.
        
        Returns:
            bool: True if the action was successfully processed, False otherwise.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)
    
    def actions_definitions_prompt(self) -&gt; str:
        &#34;&#34;&#34;
        Returns the prompt for defining a actions related to this faculty.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)

    def actions_constraints_prompt(self) -&gt; str:
        &#34;&#34;&#34;
        Returns the prompt for defining constraints on actions related to this faculty.
        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.FilesAndWebGroundingFaculty" href="#tinytroupe.agent.FilesAndWebGroundingFaculty">FilesAndWebGroundingFaculty</a></li>
<li><a title="tinytroupe.agent.RecallFaculty" href="#tinytroupe.agent.RecallFaculty">RecallFaculty</a></li>
<li><a title="tinytroupe.agent.TinyMemory" href="#tinytroupe.agent.TinyMemory">TinyMemory</a></li>
<li><a title="tinytroupe.agent.TinyToolUse" href="#tinytroupe.agent.TinyToolUse">TinyToolUse</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt"><code class="name flex">
<span>def <span class="ident">actions_constraints_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the prompt for defining constraints on actions related to this faculty.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_constraints_prompt(self) -&gt; str:
    &#34;&#34;&#34;
    Returns the prompt for defining constraints on actions related to this faculty.
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt"><code class="name flex">
<span>def <span class="ident">actions_definitions_prompt</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the prompt for defining a actions related to this faculty.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def actions_definitions_prompt(self) -&gt; str:
    &#34;&#34;&#34;
    Returns the prompt for defining a actions related to this faculty.
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyMentalFaculty.process_action"><code class="name flex">
<span>def <span class="ident">process_action</span></span>(<span>self, agent, action: dict) ‑> bool</span>
</code></dt>
<dd>
<div class="desc"><p>Processes an action related to this faculty.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>action</code></strong> :&ensp;<code>dict</code></dt>
<dd>The action to process.</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>bool</code></dt>
<dd>True if the action was successfully processed, False otherwise.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def process_action(self, agent, action: dict) -&gt; bool:
    &#34;&#34;&#34;
    Processes an action related to this faculty.

    Args:
        action (dict): The action to process.
    
    Returns:
        bool: True if the action was successfully processed, False otherwise.
    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Subclasses must implement this method.&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.agent.TinyPerson"><code class="flex name class">
<span>class <span class="ident">TinyPerson</span></span>
<span>(</span><span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>A simulated person in the TinyTroupe universe.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@post_init
class TinyPerson(JsonSerializableRegistry):
    &#34;&#34;&#34;A simulated person in the TinyTroupe universe.&#34;&#34;&#34;

    # The maximum number of actions that an agent is allowed to perform before DONE.
    # This prevents the agent from acting without ever stopping.
    MAX_ACTIONS_BEFORE_DONE = 15

    PP_TEXT_WIDTH = 100

    serializable_attributes = [&#34;name&#34;, &#34;episodic_memory&#34;, &#34;semantic_memory&#34;, &#34;_mental_faculties&#34;, &#34;_configuration&#34;]

    # A dict of all agents instantiated so far.
    all_agents = {}  # name -&gt; agent

    # The communication style for all agents: &#34;simplified&#34; or &#34;full&#34;.
    communication_style:str=&#34;simplified&#34;
    
    # Whether to display the communication or not. True is for interactive applications, when we want to see simulation
    # outputs as they are produced.
    communication_display:bool=True
    

    def __init__(self, name:str=None, 
                 episodic_memory=None,
                 semantic_memory=None,
                 mental_faculties:list=None):
        &#34;&#34;&#34;
        Creates a TinyPerson.

        Args:
            name (str): The name of the TinyPerson. Either this or spec_path must be specified.
            episodic_memory (EpisodicMemory, optional): The memory implementation to use. Defaults to EpisodicMemory().
            semantic_memory (SemanticMemory, optional): The memory implementation to use. Defaults to SemanticMemory().
            mental_faculties (list, optional): A list of mental faculties to add to the agent. Defaults to None.
        &#34;&#34;&#34;

        # NOTE: default values will be given in the _post_init method, as that&#39;s shared by 
        #       direct initialization as well as via deserialization.

        if episodic_memory is not None:
            self.episodic_memory = episodic_memory
        
        if semantic_memory is not None:
            self.semantic_memory = semantic_memory

        # Mental faculties
        if mental_faculties is not None:
            self._mental_faculties = mental_faculties
        
        assert name is not None, &#34;A TinyPerson must have a name.&#34;
        self.name = name

        # @post_init makes sure that _post_init is called after __init__

    
    def _post_init(self, **kwargs):
        &#34;&#34;&#34;
        This will run after __init__, since the class has the @post_init decorator.
        It is convenient to separate some of the initialization processes to make deserialize easier.
        &#34;&#34;&#34;

        ############################################################
        # Default values
        ############################################################

        self.current_messages = []
        
        # the current environment in which the agent is acting
        self.environment = None

        # The list of actions that this agent has performed so far, but which have not been
        # consumed by the environment yet.
        self._actions_buffer = []

        # The list of agents that this agent can currently interact with.
        # This can change over time, as agents move around the world.
        self._accessible_agents = []

        # the buffer of communications that have been displayed so far, used for
        # saving these communications to another output form later (e.g., caching)
        self._displayed_communications_buffer = []

        if not hasattr(self, &#39;episodic_memory&#39;):
            # This default value MUST NOT be in the method signature, otherwise it will be shared across all instances.
            self.episodic_memory = EpisodicMemory()
        
        if not hasattr(self, &#39;semantic_memory&#39;):
            # This default value MUST NOT be in the method signature, otherwise it will be shared across all instances.
            self.semantic_memory = SemanticMemory()
        
        # _mental_faculties
        if not hasattr(self, &#39;_mental_faculties&#39;):
            # This default value MUST NOT be in the method signature, otherwise it will be shared across all instances.
            self._mental_faculties = []

        # create the configuration dictionary
        if not hasattr(self, &#39;_configuration&#39;):          
            self._configuration = {
                &#34;name&#34;: self.name,
                &#34;age&#34;: None,
                &#34;nationality&#34;: None,
                &#34;country_of_residence&#34;: None,
                &#34;occupation&#34;: None,
                &#34;routines&#34;: [],
                &#34;occupation_description&#34;: None,
                &#34;personality_traits&#34;: [],
                &#34;professional_interests&#34;: [],
                &#34;personal_interests&#34;: [],
                &#34;skills&#34;: [],
                &#34;relationships&#34;: [],
                &#34;current_datetime&#34;: None,
                &#34;current_location&#34;: None,
                &#34;current_context&#34;: [],
                &#34;current_attention&#34;: None,
                &#34;current_goals&#34;: [],
                &#34;current_emotions&#34;: &#34;Currently you feel calm and friendly.&#34;,
                &#34;currently_accessible_agents&#34;: [],  # [{&#34;agent&#34;: agent_1, &#34;relation&#34;: &#34;My friend&#34;}, {&#34;agent&#34;: agent_2, &#34;relation&#34;: &#34;My colleague&#34;}, ...]
            }

        self._prompt_template_path = os.path.join(
            os.path.dirname(__file__), &#34;prompts/tinyperson.mustache&#34;
        )
        self._init_system_message = None  # initialized later


        ############################################################
        # Special mechanisms used during deserialization
        ############################################################

        # rename agent to some specific name?
        if kwargs.get(&#34;new_agent_name&#34;) is not None:
            self._rename(kwargs.get(&#34;new_agent_name&#34;))
        
        # If auto-rename, use the given name plus some new number ...
        if kwargs.get(&#34;auto_rename&#34;) is True:
            new_name = self.name # start with the current name
            rename_succeeded = False
            while not rename_succeeded:
                try:
                    self._rename(new_name)
                    TinyPerson.add_agent(self)
                    rename_succeeded = True                
                except ValueError:
                    new_id = utils.fresh_id()
                    new_name = f&#34;{self.name}_{new_id}&#34;
        
        # ... otherwise, just register the agent
        else:
            # register the agent in the global list of agents
            TinyPerson.add_agent(self)

        # start with a clean slate
        self.reset_prompt()

        # it could be the case that the agent is being created within a simulation scope, in which case
        # the simulation_id must be set accordingly
        if current_simulation() is not None:
            current_simulation().add_agent(self)
        else:
            self.simulation_id = None
    
    def _rename(self, new_name:str):    
        self.name = new_name
        self._configuration[&#34;name&#34;] = self.name


    def generate_agent_prompt(self):
        with open(self._prompt_template_path, &#34;r&#34;) as f:
            agent_prompt_template = f.read()

        # let&#39;s operate on top of a copy of the configuration, because we&#39;ll need to add more variables, etc.
        template_variables = self._configuration.copy()    

        # Prepare additional action definitions and constraints
        actions_definitions_prompt = &#34;&#34;
        actions_constraints_prompt = &#34;&#34;
        for faculty in self._mental_faculties:
            actions_definitions_prompt += f&#34;{faculty.actions_definitions_prompt()}\n&#34;
            actions_constraints_prompt += f&#34;{faculty.actions_constraints_prompt()}\n&#34;
        
        # make the additional prompt pieces available to the template
        template_variables[&#39;actions_definitions_prompt&#39;] = textwrap.indent(actions_definitions_prompt, &#34;&#34;)
        template_variables[&#39;actions_constraints_prompt&#39;] = textwrap.indent(actions_constraints_prompt, &#34;&#34;)

        # RAI prompt components, if requested
        template_variables = utils.add_rai_template_variables_if_enabled(template_variables)

        return chevron.render(agent_prompt_template, template_variables)

    def reset_prompt(self):

        # render the template with the current configuration
        self._init_system_message = self.generate_agent_prompt()

        # TODO actually, figure out another way to update agent state without &#34;changing history&#34;

        # reset system message
        self.current_messages = [
            {&#34;role&#34;: &#34;system&#34;, &#34;content&#34;: self._init_system_message}
        ]

        # sets up the actual interaction messages to use for prompting
        self.current_messages += self.episodic_memory.retrieve_recent()

    def get(self, key):
        &#34;&#34;&#34;
        Returns the definition of a key in the TinyPerson&#39;s configuration.
        &#34;&#34;&#34;
        return self._configuration.get(key, None)
    
    @transactional
    def define(self, key, value, group=None):
        &#34;&#34;&#34;
        Define a value to the TinyPerson&#39;s configuration.
        If group is None, the value is added to the top level of the configuration.
        Otherwise, the value is added to the specified group.
        &#34;&#34;&#34;

        # dedent value if it is a string
        if isinstance(value, str):
            value = textwrap.dedent(value)

        if group is None:
            # logger.debug(f&#34;[{self.name}] Defining {key}={value} in the person.&#34;)
            self._configuration[key] = value
        else:
            if key is not None:
                # logger.debug(f&#34;[{self.name}] Adding definition to {group} += [ {key}={value} ] in the person.&#34;)
                self._configuration[group].append({key: value})
            else:
                # logger.debug(f&#34;[{self.name}] Adding definition to {group} += [ {value} ] in the person.&#34;)
                self._configuration[group].append(value)

        # must reset prompt after adding to configuration
        self.reset_prompt()

    def define_several(self, group, records):
        &#34;&#34;&#34;
        Define several values to the TinyPerson&#39;s configuration, all belonging to the same group.
        &#34;&#34;&#34;
        for record in records:
            self.define(key=None, value=record, group=group)
    
    @transactional
    def define_relationships(self, relationships, replace=True):
        &#34;&#34;&#34;
        Defines or updates the TinyPerson&#39;s relationships.

        Args:
            relationships (list or dict): The relationships to add or replace. Either a list of dicts mapping agent names to relationship descriptions,
              or a single dict mapping one agent name to its relationship description.
            replace (bool, optional): Whether to replace the current relationships or just add to them. Defaults to True.
        &#34;&#34;&#34;
        
        if (replace == True) and (isinstance(relationships, list)):
            self._configuration[&#39;relationships&#39;] = relationships

        elif replace == False:
            current_relationships = self._configuration[&#39;relationships&#39;]
            if isinstance(relationships, list):
                for r in relationships:
                    current_relationships.append(r)
                
            elif isinstance(relationships, dict) and len(relationships) == 2: #{&#34;Name&#34;: ..., &#34;Description&#34;: ...}
                current_relationships.append(relationships)

            else:
                raise Exception(&#34;Only one key-value pair is allowed in the relationships dict.&#34;)

        else:
            raise Exception(&#34;Invalid arguments for define_relationships.&#34;)

    @transactional
    def clear_relationships(self):
        &#34;&#34;&#34;
        Clears the TinyPerson&#39;s relationships.
        &#34;&#34;&#34;
        self._configuration[&#39;relationships&#39;] = []  

        return self      
    
    @transactional
    def related_to(self, other_agent, description, symmetric_description=None):
        &#34;&#34;&#34;
        Defines a relationship between this agent and another agent.

        Args:
            other_agent (TinyPerson): The other agent.
            description (str): The description of the relationship.
            symmetric (bool): Whether the relationship is symmetric or not. That is, 
              if the relationship is defined for both agents.
        
        Returns:
            TinyPerson: The agent itself, to facilitate chaining.
        &#34;&#34;&#34;
        self.define_relationships([{&#34;Name&#34;: other_agent.name, &#34;Description&#34;: description}], replace=False)
        if symmetric_description is not None:
            other_agent.define_relationships([{&#34;Name&#34;: self.name, &#34;Description&#34;: symmetric_description}], replace=False)
        
        return self
    
    def add_mental_faculties(self, mental_faculties):
        &#34;&#34;&#34;
        Adds a list of mental faculties to the agent.
        &#34;&#34;&#34;
        for faculty in mental_faculties:
            self.add_mental_faculty(faculty)
        
        return self

    def add_mental_faculty(self, faculty):
        &#34;&#34;&#34;
        Adds a mental faculty to the agent.
        &#34;&#34;&#34;
        # check if the faculty is already there or not
        if faculty not in self._mental_faculties:
            self._mental_faculties.append(faculty)
        else:
            raise Exception(f&#34;The mental faculty {faculty} is already present in the agent.&#34;)
        
        return self

    @transactional
    def act(
        self,
        until_done=True,
        n=None,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Acts in the environment and updates its internal cognitive state.
        Either acts until the agent is done and needs additional stimuli, or acts a fixed number of times,
        but not both.

        Args:
            until_done (bool): Whether to keep acting until the agent is done and needs additional stimuli.
            n (int): The number of actions to perform. Defaults to None.
            return_actions (bool): Whether to return the actions or not. Defaults to False.
        &#34;&#34;&#34;

        # either act until done or act a fixed number of times, but not both
        assert not (until_done and n is not None)
        if n is not None:
            assert n &lt; TinyPerson.MAX_ACTIONS_BEFORE_DONE

        contents = []

        # Aux function to perform exactly one action.
        # Occasionally, the model will return JSON missing important keys, so we just ask it to try again
        @repeat_on_error(retries=5, exceptions=[KeyError])
        def aux_act_once():
            # A quick thought before the action. This seems to help with better model responses, perhaps because
            # it interleaves user with assistant messages.
            self.think(&#34;I will now act a bit, and then issue DONE.&#34;)

          
            role, content = self._produce_message()

            self.episodic_memory.store({&#39;role&#39;: role, &#39;content&#39;: content, &#39;simulation_timestamp&#39;: self.iso_datetime()})

            cognitive_state = content[&#34;cognitive_state&#34;]


            action = content[&#39;action&#39;]

            self._actions_buffer.append(action)
            self._update_cognitive_state(goals=cognitive_state[&#39;goals&#39;],
                                        attention=cognitive_state[&#39;attention&#39;],
                                        emotions=cognitive_state[&#39;emotions&#39;])
            
            contents.append(content)          
            if TinyPerson.communication_display:
                self._display_communication(role=role, content=content, kind=&#39;action&#39;, simplified=True, max_content_length=max_content_length)
            
            #
            # Some actions induce an immediate stimulus or other side-effects. We need to process them here, by means of the mental faculties.
            #
            for faculty in self._mental_faculties:
                faculty.process_action(self, action)             
            

        #
        # How to proceed with a sequence of actions.
        #

        ##### Option 1: run N actions ######
        if n is not None:
            for i in range(n):
                aux_act_once()

        ##### Option 2: run until DONE ######
        elif until_done:
            while (len(contents) == 0) or (
                not contents[-1][&#34;action&#34;][&#34;type&#34;] == &#34;DONE&#34;
            ):


                # check if the agent is acting without ever stopping
                if len(contents) &gt; TinyPerson.MAX_ACTIONS_BEFORE_DONE:
                    logger.warning(f&#34;[{self.name}] Agent {self.name} is acting without ever stopping. This may be a bug. Let&#39;s stop it here anyway.&#34;)
                    break
                if len(contents) &gt; 4: # just some minimum number of actions to check for repetition, could be anything &gt;= 3
                    # if the last three actions were the same, then we are probably in a loop
                    if contents[-1][&#39;action&#39;] == contents[-2][&#39;action&#39;] == contents[-3][&#39;action&#39;]:
                        logger.warning(f&#34;[{self.name}] Agent {self.name} is acting in a loop. This may be a bug. Let&#39;s stop it here anyway.&#34;)
                        break

                aux_act_once()

        if return_actions:
            return contents

    @transactional
    def listen(
        self,
        speech,
        source: AgentOrWorld = None,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Listens to another agent (artificial or human) and updates its internal cognitive state.

        Args:
            speech (str): The speech to listen to.
            source (AgentOrWorld, optional): The source of the speech. Defaults to None.
        &#34;&#34;&#34;

        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;CONVERSATION&#34;,
                &#34;content&#34;: speech,
                &#34;source&#34;: name_or_empty(source),
            },
            max_content_length=max_content_length,
        )

    def socialize(
        self,
        social_description: str,
        source: AgentOrWorld = None,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Perceives a social stimulus through a description and updates its internal cognitive state.

        Args:
            social_description (str): The description of the social stimulus.
            source (AgentOrWorld, optional): The source of the social stimulus. Defaults to None.
        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;SOCIAL&#34;,
                &#34;content&#34;: social_description,
                &#34;source&#34;: name_or_empty(source),
            },
            max_content_length=max_content_length,
        )

    def see(
        self,
        visual_description,
        source: AgentOrWorld = None,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Perceives a visual stimulus through a description and updates its internal cognitive state.

        Args:
            visual_description (str): The description of the visual stimulus.
            source (AgentOrWorld, optional): The source of the visual stimulus. Defaults to None.
        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;VISUAL&#34;,
                &#34;content&#34;: visual_description,
                &#34;source&#34;: name_or_empty(source),
            },
            max_content_length=max_content_length,
        )

    def think(self, thought, max_content_length=default[&#34;max_content_display_length&#34;]):
        &#34;&#34;&#34;
        Forces the agent to think about something and updates its internal cognitive state.

        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;THOUGHT&#34;,
                &#34;content&#34;: thought,
                &#34;source&#34;: name_or_empty(self),
            },
            max_content_length=max_content_length,
        )

    def internalize_goal(
        self, goal, max_content_length=default[&#34;max_content_display_length&#34;]
    ):
        &#34;&#34;&#34;
        Internalizes a goal and updates its internal cognitive state.
        &#34;&#34;&#34;
        return self._observe(
            stimulus={
                &#34;type&#34;: &#34;INTERNAL_GOAL_FORMULATION&#34;,
                &#34;content&#34;: goal,
                &#34;source&#34;: name_or_empty(self),
            },
            max_content_length=max_content_length,
        )

    @transactional
    def _observe(self, stimulus, max_content_length=default[&#34;max_content_display_length&#34;]):
        stimuli = [stimulus]

        content = {&#34;stimuli&#34;: stimuli}

        logger.debug(f&#34;[{self.name}] Observing stimuli: {content}&#34;)

        # whatever comes from the outside will be interpreted as coming from &#39;user&#39;, simply because
        # this is the counterpart of &#39;assistant&#39;

        self.episodic_memory.store({&#39;role&#39;: &#39;user&#39;, &#39;content&#39;: content, &#39;simulation_timestamp&#39;: self.iso_datetime()})

        if TinyPerson.communication_display:
            self._display_communication(
                role=&#34;user&#34;,
                content=content,
                kind=&#34;stimuli&#34;,
                simplified=True,
                max_content_length=max_content_length,
            )

        return self  # allows easier chaining of methods

    @transactional
    def listen_and_act(
        self,
        speech,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Convenience method that combines the `listen` and `act` methods.
        &#34;&#34;&#34;

        self.listen(speech, max_content_length=max_content_length)
        return self.act(
            return_actions=return_actions, max_content_length=max_content_length
        )

    @transactional
    def see_and_act(
        self,
        visual_description,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Convenience method that combines the `see` and `act` methods.
        &#34;&#34;&#34;

        self.see(visual_description, max_content_length=max_content_length)
        return self.act(
            return_actions=return_actions, max_content_length=max_content_length
        )

    @transactional
    def think_and_act(
        self,
        thought,
        return_actions=False,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Convenience method that combines the `think` and `act` methods.
        &#34;&#34;&#34;

        self.think(thought, max_content_length=max_content_length)
        return self.act(return_actions=return_actions, max_content_length=max_content_length)

    def read_documents_from_folder(self, documents_path:str):
        &#34;&#34;&#34;
        Reads documents from a directory and loads them into the semantic memory.
        &#34;&#34;&#34;
        logger.info(f&#34;Setting documents path to {documents_path} and loading documents.&#34;)

        self.semantic_memory.add_documents_path(documents_path)
    
    def read_documents_from_web(self, web_urls:list):
        &#34;&#34;&#34;
        Reads documents from web URLs and loads them into the semantic memory.
        &#34;&#34;&#34;
        logger.info(f&#34;Reading documents from the following web URLs: {web_urls}&#34;)

        self.semantic_memory.add_web_urls(web_urls)
    
    @transactional
    def move_to(self, location, context=[]):
        &#34;&#34;&#34;
        Moves to a new location and updates its internal cognitive state.
        &#34;&#34;&#34;
        self._configuration[&#34;current_location&#34;] = location

        # context must also be updated when moved, since we assume that context is dictated partly by location.
        self.change_context(context)

    @transactional
    def change_context(self, context: list):
        &#34;&#34;&#34;
        Changes the context and updates its internal cognitive state.
        &#34;&#34;&#34;
        self._configuration[&#34;current_context&#34;] = {
            &#34;description&#34;: item for item in context
        }

        self._update_cognitive_state(context=context)

    @transactional
    def make_agent_accessible(
        self,
        agent: Self,
        relation_description: str = &#34;An agent I can currently interact with.&#34;,
    ):
        &#34;&#34;&#34;
        Makes an agent accessible to this agent.
        &#34;&#34;&#34;
        if agent not in self._accessible_agents:
            self._accessible_agents.append(agent)
            self._configuration[&#34;currently_accessible_agents&#34;].append(
                {&#34;name&#34;: agent.name, &#34;relation_description&#34;: relation_description}
            )
        else:
            logger.warning(
                f&#34;[{self.name}] Agent {agent.name} is already accessible to {self.name}.&#34;
            )

    @transactional
    def make_agent_inaccessible(self, agent: Self):
        &#34;&#34;&#34;
        Makes an agent inaccessible to this agent.
        &#34;&#34;&#34;
        if agent in self._accessible_agents:
            self._accessible_agents.remove(agent)
        else:
            logger.warning(
                f&#34;[{self.name}] Agent {agent.name} is already inaccessible to {self.name}.&#34;
            )

    @transactional
    def make_all_agents_inaccessible(self):
        &#34;&#34;&#34;
        Makes all agents inaccessible to this agent.
        &#34;&#34;&#34;
        self._accessible_agents = []
        self._configuration[&#34;currently_accessible_agents&#34;] = []

    @transactional
    def _produce_message(self):
        # logger.debug(f&#34;Current messages: {self.current_messages}&#34;)

        # ensure we have the latest prompt (initial system message + selected messages from memory)
        self.reset_prompt()

        messages = [
            {&#34;role&#34;: msg[&#34;role&#34;], &#34;content&#34;: json.dumps(msg[&#34;content&#34;])}
            for msg in self.current_messages
        ]

        logger.debug(f&#34;[{self.name}] Sending messages to OpenAI API&#34;)
        logger.debug(f&#34;[{self.name}] Last interaction: {messages[-1]}&#34;)

        next_message = openai_utils.client().send_message(messages)

        logger.debug(f&#34;[{self.name}] Received message: {next_message}&#34;)

        return next_message[&#34;role&#34;], utils.extract_json(next_message[&#34;content&#34;])

    ###########################################################
    # Internal cognitive state changes
    ###########################################################
    @transactional
    def _update_cognitive_state(
        self, goals=None, context=None, attention=None, emotions=None
    ):
        &#34;&#34;&#34;
        Update the TinyPerson&#39;s cognitive state.
        &#34;&#34;&#34;

        # Update current datetime. The passage of time is controlled by the environment, if any.
        if self.environment is not None and self.environment.current_datetime is not None:
            self._configuration[&#34;current_datetime&#34;] = utils.pretty_datetime(self.environment.current_datetime)

        # update current goals
        if goals is not None:
            self._configuration[&#34;current_goals&#34;] = goals

        # update current context
        if context is not None:
            self._configuration[&#34;current_context&#34;] = context

        # update current attention
        if attention is not None:
            self._configuration[&#34;current_attention&#34;] = attention

        # update current emotions
        if emotions is not None:
            self._configuration[&#34;current_emotions&#34;] = emotions

        self.reset_prompt()

    ###########################################################
    # Inspection conveniences
    ###########################################################
    def _display_communication(
        self,
        role,
        content,
        kind,
        simplified=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Displays the current communication and stores it in a buffer for later use.
        &#34;&#34;&#34;
        if kind == &#34;stimuli&#34;:
            rendering = self._pretty_stimuli(
                role=role,
                content=content,
                simplified=simplified,
                max_content_length=max_content_length,
            )
        elif kind == &#34;action&#34;:
            rendering = self._pretty_action(
                role=role,
                content=content,
                simplified=simplified,
                max_content_length=max_content_length,
            )
        else:
            raise ValueError(f&#34;Unknown communication kind: {kind}&#34;)

        # if the agent has no parent environment, then it is a free agent and we can display the communication.
        # otherwise, the environment will display the communication instead. This is important to make sure that
        # the communication is displayed in the correct order, since environments control the flow of their underlying
        # agents.
        if self.environment is None:
            self._push_and_display_latest_communication(rendering)
        else:
            self.environment._push_and_display_latest_communication(rendering)

    def _push_and_display_latest_communication(self, rendering):
        &#34;&#34;&#34;
        Pushes the latest communications to the agent&#39;s buffer.
        &#34;&#34;&#34;
        self._displayed_communications_buffer.append(rendering)
        print(rendering)

    def pop_and_display_latest_communications(self):
        &#34;&#34;&#34;
        Pops the latest communications and displays them.
        &#34;&#34;&#34;
        communications = self._displayed_communications_buffer
        self._displayed_communications_buffer = []

        for communication in communications:
            print(communication)

        return communications

    def clear_communications_buffer(self):
        &#34;&#34;&#34;
        Cleans the communications buffer.
        &#34;&#34;&#34;
        self._displayed_communications_buffer = []

    @transactional
    def pop_latest_actions(self) -&gt; list:
        &#34;&#34;&#34;
        Returns the latest actions performed by this agent. Typically used
        by an environment to consume the actions and provide the appropriate
        environmental semantics to them (i.e., effects on other agents).
        &#34;&#34;&#34;
        actions = self._actions_buffer
        self._actions_buffer = []
        return actions

    @transactional
    def pop_actions_and_get_contents_for(
        self, action_type: str, only_last_action: bool = True
    ) -&gt; list:
        &#34;&#34;&#34;
        Returns the contents of actions of a given type performed by this agent.
        Typically used to perform inspections and tests.

        Args:
            action_type (str): The type of action to look for.
            only_last_action (bool, optional): Whether to only return the contents of the last action. Defaults to False.
        &#34;&#34;&#34;
        actions = self.pop_latest_actions()
        # Filter the actions by type
        actions = [action for action in actions if action[&#34;type&#34;] == action_type]

        # If interested only in the last action, return the latest one
        if only_last_action:
            return actions[-1].get(&#34;content&#34;, &#34;&#34;)

        # Otherwise, return all contents from the filtered actions
        return &#34;\n&#34;.join([action.get(&#34;content&#34;, &#34;&#34;) for action in actions])

    #############################################################################################
    # Formatting conveniences
    #
    # For rich colors,
    #    see: https://rich.readthedocs.io/en/latest/appendix/colors.html#appendix-colors
    #############################################################################################

    def __repr__(self):
        return f&#34;TinyPerson(name=&#39;{self.name}&#39;)&#34;

    def minibio(self):
        &#34;&#34;&#34;
        Returns a mini-biography of the TinyPerson.
        &#34;&#34;&#34;
        return f&#34;{self.name} is a {self._configuration[&#39;age&#39;]} year old {self._configuration[&#39;occupation&#39;]}, {self._configuration[&#39;nationality&#39;]}, currently living in {self._configuration[&#39;country_of_residence&#39;]}.&#34;

    def pp_current_interactions(
        self,
        simplified=True,
        skip_system=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ):
        &#34;&#34;&#34;
        Pretty prints the current messages.
        &#34;&#34;&#34;
        print(
            self.pretty_current_interactions(
                simplified=simplified,
                skip_system=skip_system,
                max_content_length=max_content_length,
            )
        )

    def pretty_current_interactions(self, simplified=True, skip_system=True, max_content_length=default[&#34;max_content_display_length&#34;], first_n=None, last_n=None, include_omission_info:bool=True):
      &#34;&#34;&#34;
      Returns a pretty, readable, string with the current messages.
      &#34;&#34;&#34;
      lines = []
      for message in self.episodic_memory.retrieve(first_n=first_n, last_n=last_n, include_omission_info=include_omission_info):
        try:
            if not (skip_system and message[&#39;role&#39;] == &#39;system&#39;):
                msg_simplified_type = &#34;&#34;
                msg_simplified_content = &#34;&#34;
                msg_simplified_actor = &#34;&#34;

                lines.append(self._pretty_timestamp(message[&#39;role&#39;], message[&#39;simulation_timestamp&#39;]))

                if message[&#34;role&#34;] == &#34;system&#34;:
                    msg_simplified_actor = &#34;SYSTEM&#34;
                    msg_simplified_type = message[&#34;role&#34;]
                    msg_simplified_content = message[&#34;content&#34;]

                    lines.append(
                        f&#34;[dim] {msg_simplified_type}: {msg_simplified_content}[/]&#34;
                    )

                elif message[&#34;role&#34;] == &#34;user&#34;:
                    lines.append(
                        self._pretty_stimuli(
                            role=message[&#34;role&#34;],
                            content=message[&#34;content&#34;],
                            simplified=simplified,
                            max_content_length=max_content_length,
                        )
                    )

                elif message[&#34;role&#34;] == &#34;assistant&#34;:
                    lines.append(
                        self._pretty_action(
                            role=message[&#34;role&#34;],
                            content=message[&#34;content&#34;],
                            simplified=simplified,
                            max_content_length=max_content_length,
                        )
                    )
                else:
                    lines.append(f&#34;{message[&#39;role&#39;]}: {message[&#39;content&#39;]}&#34;)
        except:
            # print(f&#34;ERROR: {message}&#34;)
            continue

      return &#34;\n&#34;.join(lines)

    def _pretty_stimuli(
        self,
        role,
        content,
        simplified=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ) -&gt; list:
        &#34;&#34;&#34;
        Pretty prints stimuli.
        &#34;&#34;&#34;

        lines = []
        msg_simplified_actor = &#34;USER&#34;
        for stimus in content[&#34;stimuli&#34;]:
            if simplified:
                if stimus[&#34;source&#34;] != &#34;&#34;:
                    msg_simplified_actor = stimus[&#34;source&#34;]

                else:
                    msg_simplified_actor = &#34;USER&#34;

                msg_simplified_type = stimus[&#34;type&#34;]
                msg_simplified_content = break_text_at_length(
                    stimus[&#34;content&#34;], max_length=max_content_length
                )

                indent = &#34; &#34; * len(msg_simplified_actor) + &#34;      &gt; &#34;
                msg_simplified_content = textwrap.fill(
                    msg_simplified_content,
                    width=TinyPerson.PP_TEXT_WIDTH,
                    initial_indent=indent,
                    subsequent_indent=indent,
                )

                #
                # Using rich for formatting. Let&#39;s make things as readable as possible!
                #
                if msg_simplified_type == &#34;CONVERSATION&#34;:
                    rich_style = &#34;bold italic cyan1&#34;
                elif msg_simplified_type == &#34;THOUGHT&#34;:
                    rich_style = &#34;dim italic cyan1&#34;
                else:
                    rich_style = &#34;italic&#34;

                lines.append(
                    f&#34;[{rich_style}][underline]{msg_simplified_actor}[/] --&gt; [{rich_style}][underline]{self.name}[/]: [{msg_simplified_type}] \n{msg_simplified_content}[/]&#34;
                )
            else:
                lines.append(f&#34;{role}: {content}&#34;)

        return &#34;\n&#34;.join(lines)

    def _pretty_action(
        self,
        role,
        content,
        simplified=True,
        max_content_length=default[&#34;max_content_display_length&#34;],
    ) -&gt; str:
        &#34;&#34;&#34;
        Pretty prints an action.
        &#34;&#34;&#34;
        if simplified:
            msg_simplified_actor = self.name
            msg_simplified_type = content[&#34;action&#34;][&#34;type&#34;]
            msg_simplified_content = break_text_at_length(
                content[&#34;action&#34;].get(&#34;content&#34;, &#34;&#34;), max_length=max_content_length
            )

            indent = &#34; &#34; * len(msg_simplified_actor) + &#34;      &gt; &#34;
            msg_simplified_content = textwrap.fill(
                msg_simplified_content,
                width=TinyPerson.PP_TEXT_WIDTH,
                initial_indent=indent,
                subsequent_indent=indent,
            )

            #
            # Using rich for formatting. Let&#39;s make things as readable as possible!
            #
            if msg_simplified_type == &#34;DONE&#34;:
                rich_style = &#34;grey82&#34;
            elif msg_simplified_type == &#34;TALK&#34;:
                rich_style = &#34;bold green3&#34;
            elif msg_simplified_type == &#34;THINK&#34;:
                rich_style = &#34;green&#34;
            else:
                rich_style = &#34;purple&#34;

            return f&#34;[{rich_style}][underline]{msg_simplified_actor}[/] acts: [{msg_simplified_type}] \n{msg_simplified_content}[/]&#34;
        else:
            return f&#34;{role}: {content}&#34;
    
    def _pretty_timestamp(
        self,
        role,
        timestamp,
    ) -&gt; str:
        &#34;&#34;&#34;
        Pretty prints a timestamp.
        &#34;&#34;&#34;
        return f&#34;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt; Date and time of events: {timestamp}&#34;

    def iso_datetime(self) -&gt; str:
        &#34;&#34;&#34;
        Returns the current datetime of the environment, if any.

        Returns:
            datetime: The current datetime of the environment in ISO forat.
        &#34;&#34;&#34;
        if self.environment is not None and self.environment.current_datetime is not None:
            return self.environment.current_datetime.isoformat()
        else:
            return None

    ###########################################################
    # IO
    ###########################################################

    def save_spec(self, path, include_mental_faculties=True, include_memory=False):
        &#34;&#34;&#34;
        Saves the current configuration to a JSON file.
        &#34;&#34;&#34;
        
        suppress_attributes = []

        # should we include the memory?
        if not include_memory:
            suppress_attributes.append(&#34;episodic_memory&#34;)
            suppress_attributes.append(&#34;semantic_memory&#34;)

        # should we include the mental faculties?
        if not include_mental_faculties:
            suppress_attributes.append(&#34;_mental_faculties&#34;)

        self.to_json(suppress=suppress_attributes, file_path=path)

    
    @staticmethod
    def load_spec(path, suppress_mental_faculties=False, suppress_memory=False, auto_rename_agent=False, new_agent_name=None):
        &#34;&#34;&#34;
        Loads a JSON agent specification.

        Args:
            path (str): The path to the JSON file containing the agent specification.
            suppress_mental_faculties (bool, optional): Whether to suppress loading the mental faculties. Defaults to False.
            suppress_memory (bool, optional): Whether to suppress loading the memory. Defaults to False.
        &#34;&#34;&#34;

        suppress_attributes = []

        # should we suppress the mental faculties?
        if suppress_mental_faculties:
            suppress_attributes.append(&#34;_mental_faculties&#34;)

        # should we suppress the memory?
        if suppress_memory:
            suppress_attributes.append(&#34;episodic_memory&#34;)
            suppress_attributes.append(&#34;semantic_memory&#34;)

        return TinyPerson.from_json(json_dict_or_path=path, suppress=suppress_attributes, 
                                    post_init_params={&#34;auto_rename_agent&#34;: auto_rename_agent, &#34;new_agent_name&#34;: new_agent_name})


    def encode_complete_state(self) -&gt; dict:
        &#34;&#34;&#34;
        Encodes the complete state of the TinyPerson, including the current messages, accessible agents, etc.
        This is meant for serialization and caching purposes, not for exporting the state to the user.
        &#34;&#34;&#34;
        to_copy = copy.copy(self.__dict__)

        # delete the logger and other attributes that cannot be serialized
        del to_copy[&#34;environment&#34;]
        del to_copy[&#34;_mental_faculties&#34;]

        to_copy[&#34;_accessible_agents&#34;] = [agent.name for agent in self._accessible_agents]
        to_copy[&#39;episodic_memory&#39;] = self.episodic_memory.to_json()
        to_copy[&#39;semantic_memory&#39;] = self.semantic_memory.to_json()
        to_copy[&#34;_mental_faculties&#34;] = [faculty.to_json() for faculty in self._mental_faculties]

        state = copy.deepcopy(to_copy)

        return state

    def decode_complete_state(self, state: dict) -&gt; Self:
        &#34;&#34;&#34;
        Loads the complete state of the TinyPerson, including the current messages,
        and produces a new TinyPerson instance.
        &#34;&#34;&#34;
        state = copy.deepcopy(state)
        
        self._accessible_agents = [TinyPerson.get_agent_by_name(name) for name in state[&#34;_accessible_agents&#34;]]
        self.episodic_memory = EpisodicMemory.from_json(state[&#39;episodic_memory&#39;])
        self.semantic_memory = SemanticMemory.from_json(state[&#39;semantic_memory&#39;])
        
        for i, faculty in enumerate(self._mental_faculties):
            faculty = faculty.from_json(state[&#39;_mental_faculties&#39;][i])

        # delete fields already present in the state
        del state[&#34;_accessible_agents&#34;]
        del state[&#39;episodic_memory&#39;]
        del state[&#39;semantic_memory&#39;]
        del state[&#39;_mental_faculties&#39;]

        # restore other fields
        self.__dict__.update(state)


        return self
    
    def create_new_agent_from_current_spec(self, new_name:str) -&gt; Self:
        &#34;&#34;&#34;
        Creates a new agent from the current agent&#39;s specification. 

        Args:
            new_name (str): The name of the new agent. Agent names must be unique in the simulation, 
              this is why we need to provide a new name.
        &#34;&#34;&#34;
        new_agent = TinyPerson(name=new_name, spec_path=None)
        
        new_config = copy.deepcopy(self._configuration)
        new_config[&#39;name&#39;] = new_name

        new_agent._configuration = new_config

        return new_agent
        

    @staticmethod
    def add_agent(agent):
        &#34;&#34;&#34;
        Adds an agent to the global list of agents. Agent names must be unique,
        so this method will raise an exception if the name is already in use.
        &#34;&#34;&#34;
        if agent.name in TinyPerson.all_agents:
            raise ValueError(f&#34;Agent name {agent.name} is already in use.&#34;)
        else:
            TinyPerson.all_agents[agent.name] = agent

    @staticmethod
    def has_agent(agent_name: str):
        &#34;&#34;&#34;
        Checks if an agent is already registered.
        &#34;&#34;&#34;
        return agent_name in TinyPerson.all_agents

    @staticmethod
    def set_simulation_for_free_agents(simulation):
        &#34;&#34;&#34;
        Sets the simulation if it is None. This allows free agents to be captured by specific simulation scopes
        if desired.
        &#34;&#34;&#34;
        for agent in TinyPerson.all_agents.values():
            if agent.simulation_id is None:
                simulation.add_agent(agent)

    @staticmethod
    def get_agent_by_name(name):
        &#34;&#34;&#34;
        Gets an agent by name.
        &#34;&#34;&#34;
        if name in TinyPerson.all_agents:
            return TinyPerson.all_agents[name]
        else:
            return None

    @staticmethod
    def clear_agents():
        &#34;&#34;&#34;
        Clears the global list of agents.
        &#34;&#34;&#34;
        TinyPerson.all_agents = {}        </code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="tinytroupe.agent.TinyPerson.MAX_ACTIONS_BEFORE_DONE"><code class="name">var <span class="ident">MAX_ACTIONS_BEFORE_DONE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="tinytroupe.agent.TinyPerson.PP_TEXT_WIDTH"><code class="name">var <span class="ident">PP_TEXT_WIDTH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="tinytroupe.agent.TinyPerson.all_agents"><code class="name">var <span class="ident">all_agents</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="tinytroupe.agent.TinyPerson.communication_display"><code class="name">var <span class="ident">communication_display</span> : bool</code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="tinytroupe.agent.TinyPerson.communication_style"><code class="name">var <span class="ident">communication_style</span> : str</code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="tinytroupe.agent.TinyPerson.serializable_attributes"><code class="name">var <span class="ident">serializable_attributes</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
<h3>Static methods</h3>
<dl>
<dt id="tinytroupe.agent.TinyPerson.add_agent"><code class="name flex">
<span>def <span class="ident">add_agent</span></span>(<span>agent)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds an agent to the global list of agents. Agent names must be unique,
so this method will raise an exception if the name is already in use.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@staticmethod
def add_agent(agent):
    &#34;&#34;&#34;
    Adds an agent to the global list of agents. Agent names must be unique,
    so this method will raise an exception if the name is already in use.
    &#34;&#34;&#34;
    if agent.name in TinyPerson.all_agents:
        raise ValueError(f&#34;Agent name {agent.name} is already in use.&#34;)
    else:
        TinyPerson.all_agents[agent.name] = agent</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.clear_agents"><code class="name flex">
<span>def <span class="ident">clear_agents</span></span>(<span>)</span>
</code></dt>
<dd>
<div class="desc"><p>Clears the global list of agents.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@staticmethod
def clear_agents():
    &#34;&#34;&#34;
    Clears the global list of agents.
    &#34;&#34;&#34;
    TinyPerson.all_agents = {}        </code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.get_agent_by_name"><code class="name flex">
<span>def <span class="ident">get_agent_by_name</span></span>(<span>name)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets an agent by name.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@staticmethod
def get_agent_by_name(name):
    &#34;&#34;&#34;
    Gets an agent by name.
    &#34;&#34;&#34;
    if name in TinyPerson.all_agents:
        return TinyPerson.all_agents[name]
    else:
        return None</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.has_agent"><code class="name flex">
<span>def <span class="ident">has_agent</span></span>(<span>agent_name: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if an agent is already registered.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@staticmethod
def has_agent(agent_name: str):
    &#34;&#34;&#34;
    Checks if an agent is already registered.
    &#34;&#34;&#34;
    return agent_name in TinyPerson.all_agents</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.load_spec"><code class="name flex">
<span>def <span class="ident">load_spec</span></span>(<span>path, suppress_mental_faculties=False, suppress_memory=False, auto_rename_agent=False, new_agent_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Loads a JSON agent specification.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>path</code></strong> :&ensp;<code>str</code></dt>
<dd>The path to the JSON file containing the agent specification.</dd>
<dt><strong><code>suppress_mental_faculties</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to suppress loading the mental faculties. Defaults to False.</dd>
<dt><strong><code>suppress_memory</code></strong> :&ensp;<code>bool</code>, optional</dt>
<dd>Whether to suppress loading the memory. Defaults to False.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@staticmethod
def load_spec(path, suppress_mental_faculties=False, suppress_memory=False, auto_rename_agent=False, new_agent_name=None):
    &#34;&#34;&#34;
    Loads a JSON agent specification.

    Args:
        path (str): The path to the JSON file containing the agent specification.
        suppress_mental_faculties (bool, optional): Whether to suppress loading the mental faculties. Defaults to False.
        suppress_memory (bool, optional): Whether to suppress loading the memory. Defaults to False.
    &#34;&#34;&#34;

    suppress_attributes = []

    # should we suppress the mental faculties?
    if suppress_mental_faculties:
        suppress_attributes.append(&#34;_mental_faculties&#34;)

    # should we suppress the memory?
    if suppress_memory:
        suppress_attributes.append(&#34;episodic_memory&#34;)
        suppress_attributes.append(&#34;semantic_memory&#34;)

    return TinyPerson.from_json(json_dict_or_path=path, suppress=suppress_attributes, 
                                post_init_params={&#34;auto_rename_agent&#34;: auto_rename_agent, &#34;new_agent_name&#34;: new_agent_name})</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.set_simulation_for_free_agents"><code class="name flex">
<span>def <span class="ident">set_simulation_for_free_agents</span></span>(<span>simulation)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the simulation if it is None. This allows free agents to be captured by specific simulation scopes
if desired.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">@staticmethod
def set_simulation_for_free_agents(simulation):
    &#34;&#34;&#34;
    Sets the simulation if it is None. This allows free agents to be captured by specific simulation scopes
    if desired.
    &#34;&#34;&#34;
    for agent in TinyPerson.all_agents.values():
        if agent.simulation_id is None:
            simulation.add_agent(agent)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="tinytroupe.agent.TinyPerson.act"><code class="name flex">
<span>def <span class="ident">act</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.add_mental_faculties"><code class="name flex">
<span>def <span class="ident">add_mental_faculties</span></span>(<span>self, mental_faculties)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a list of mental faculties to the agent.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_mental_faculties(self, mental_faculties):
    &#34;&#34;&#34;
    Adds a list of mental faculties to the agent.
    &#34;&#34;&#34;
    for faculty in mental_faculties:
        self.add_mental_faculty(faculty)
    
    return self</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.add_mental_faculty"><code class="name flex">
<span>def <span class="ident">add_mental_faculty</span></span>(<span>self, faculty)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a mental faculty to the agent.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def add_mental_faculty(self, faculty):
    &#34;&#34;&#34;
    Adds a mental faculty to the agent.
    &#34;&#34;&#34;
    # check if the faculty is already there or not
    if faculty not in self._mental_faculties:
        self._mental_faculties.append(faculty)
    else:
        raise Exception(f&#34;The mental faculty {faculty} is already present in the agent.&#34;)
    
    return self</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.change_context"><code class="name flex">
<span>def <span class="ident">change_context</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.clear_communications_buffer"><code class="name flex">
<span>def <span class="ident">clear_communications_buffer</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Cleans the communications buffer.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def clear_communications_buffer(self):
    &#34;&#34;&#34;
    Cleans the communications buffer.
    &#34;&#34;&#34;
    self._displayed_communications_buffer = []</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.clear_relationships"><code class="name flex">
<span>def <span class="ident">clear_relationships</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.create_new_agent_from_current_spec"><code class="name flex">
<span>def <span class="ident">create_new_agent_from_current_spec</span></span>(<span>self, new_name: str) ‑> ~Self</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a new agent from the current agent's specification. </p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>new_name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the new agent. Agent names must be unique in the simulation,
this is why we need to provide a new name.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def create_new_agent_from_current_spec(self, new_name:str) -&gt; Self:
    &#34;&#34;&#34;
    Creates a new agent from the current agent&#39;s specification. 

    Args:
        new_name (str): The name of the new agent. Agent names must be unique in the simulation, 
          this is why we need to provide a new name.
    &#34;&#34;&#34;
    new_agent = TinyPerson(name=new_name, spec_path=None)
    
    new_config = copy.deepcopy(self._configuration)
    new_config[&#39;name&#39;] = new_name

    new_agent._configuration = new_config

    return new_agent</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.decode_complete_state"><code class="name flex">
<span>def <span class="ident">decode_complete_state</span></span>(<span>self, state: dict) ‑> ~Self</span>
</code></dt>
<dd>
<div class="desc"><p>Loads the complete state of the TinyPerson, including the current messages,
and produces a new TinyPerson instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def decode_complete_state(self, state: dict) -&gt; Self:
    &#34;&#34;&#34;
    Loads the complete state of the TinyPerson, including the current messages,
    and produces a new TinyPerson instance.
    &#34;&#34;&#34;
    state = copy.deepcopy(state)
    
    self._accessible_agents = [TinyPerson.get_agent_by_name(name) for name in state[&#34;_accessible_agents&#34;]]
    self.episodic_memory = EpisodicMemory.from_json(state[&#39;episodic_memory&#39;])
    self.semantic_memory = SemanticMemory.from_json(state[&#39;semantic_memory&#39;])
    
    for i, faculty in enumerate(self._mental_faculties):
        faculty = faculty.from_json(state[&#39;_mental_faculties&#39;][i])

    # delete fields already present in the state
    del state[&#34;_accessible_agents&#34;]
    del state[&#39;episodic_memory&#39;]
    del state[&#39;semantic_memory&#39;]
    del state[&#39;_mental_faculties&#39;]

    # restore other fields
    self.__dict__.update(state)


    return self</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.define"><code class="name flex">
<span>def <span class="ident">define</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.define_relationships"><code class="name flex">
<span>def <span class="ident">define_relationships</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.define_several"><code class="name flex">
<span>def <span class="ident">define_several</span></span>(<span>self, group, records)</span>
</code></dt>
<dd>
<div class="desc"><p>Define several values to the TinyPerson's configuration, all belonging to the same group.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def define_several(self, group, records):
    &#34;&#34;&#34;
    Define several values to the TinyPerson&#39;s configuration, all belonging to the same group.
    &#34;&#34;&#34;
    for record in records:
        self.define(key=None, value=record, group=group)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.encode_complete_state"><code class="name flex">
<span>def <span class="ident">encode_complete_state</span></span>(<span>self) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Encodes the complete state of the TinyPerson, including the current messages, accessible agents, etc.
This is meant for serialization and caching purposes, not for exporting the state to the user.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def encode_complete_state(self) -&gt; dict:
    &#34;&#34;&#34;
    Encodes the complete state of the TinyPerson, including the current messages, accessible agents, etc.
    This is meant for serialization and caching purposes, not for exporting the state to the user.
    &#34;&#34;&#34;
    to_copy = copy.copy(self.__dict__)

    # delete the logger and other attributes that cannot be serialized
    del to_copy[&#34;environment&#34;]
    del to_copy[&#34;_mental_faculties&#34;]

    to_copy[&#34;_accessible_agents&#34;] = [agent.name for agent in self._accessible_agents]
    to_copy[&#39;episodic_memory&#39;] = self.episodic_memory.to_json()
    to_copy[&#39;semantic_memory&#39;] = self.semantic_memory.to_json()
    to_copy[&#34;_mental_faculties&#34;] = [faculty.to_json() for faculty in self._mental_faculties]

    state = copy.deepcopy(to_copy)

    return state</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.generate_agent_prompt"><code class="name flex">
<span>def <span class="ident">generate_agent_prompt</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def generate_agent_prompt(self):
    with open(self._prompt_template_path, &#34;r&#34;) as f:
        agent_prompt_template = f.read()

    # let&#39;s operate on top of a copy of the configuration, because we&#39;ll need to add more variables, etc.
    template_variables = self._configuration.copy()    

    # Prepare additional action definitions and constraints
    actions_definitions_prompt = &#34;&#34;
    actions_constraints_prompt = &#34;&#34;
    for faculty in self._mental_faculties:
        actions_definitions_prompt += f&#34;{faculty.actions_definitions_prompt()}\n&#34;
        actions_constraints_prompt += f&#34;{faculty.actions_constraints_prompt()}\n&#34;
    
    # make the additional prompt pieces available to the template
    template_variables[&#39;actions_definitions_prompt&#39;] = textwrap.indent(actions_definitions_prompt, &#34;&#34;)
    template_variables[&#39;actions_constraints_prompt&#39;] = textwrap.indent(actions_constraints_prompt, &#34;&#34;)

    # RAI prompt components, if requested
    template_variables = utils.add_rai_template_variables_if_enabled(template_variables)

    return chevron.render(agent_prompt_template, template_variables)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, key)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the definition of a key in the TinyPerson's configuration.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def get(self, key):
    &#34;&#34;&#34;
    Returns the definition of a key in the TinyPerson&#39;s configuration.
    &#34;&#34;&#34;
    return self._configuration.get(key, None)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.internalize_goal"><code class="name flex">
<span>def <span class="ident">internalize_goal</span></span>(<span>self, goal, max_content_length=1024)</span>
</code></dt>
<dd>
<div class="desc"><p>Internalizes a goal and updates its internal cognitive state.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def internalize_goal(
    self, goal, max_content_length=default[&#34;max_content_display_length&#34;]
):
    &#34;&#34;&#34;
    Internalizes a goal and updates its internal cognitive state.
    &#34;&#34;&#34;
    return self._observe(
        stimulus={
            &#34;type&#34;: &#34;INTERNAL_GOAL_FORMULATION&#34;,
            &#34;content&#34;: goal,
            &#34;source&#34;: name_or_empty(self),
        },
        max_content_length=max_content_length,
    )</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.iso_datetime"><code class="name flex">
<span>def <span class="ident">iso_datetime</span></span>(<span>self) ‑> str</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the current datetime of the environment, if any.</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>datetime</code></dt>
<dd>The current datetime of the environment in ISO forat.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def iso_datetime(self) -&gt; str:
    &#34;&#34;&#34;
    Returns the current datetime of the environment, if any.

    Returns:
        datetime: The current datetime of the environment in ISO forat.
    &#34;&#34;&#34;
    if self.environment is not None and self.environment.current_datetime is not None:
        return self.environment.current_datetime.isoformat()
    else:
        return None</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.listen"><code class="name flex">
<span>def <span class="ident">listen</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.listen_and_act"><code class="name flex">
<span>def <span class="ident">listen_and_act</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.make_agent_accessible"><code class="name flex">
<span>def <span class="ident">make_agent_accessible</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.make_agent_inaccessible"><code class="name flex">
<span>def <span class="ident">make_agent_inaccessible</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.make_all_agents_inaccessible"><code class="name flex">
<span>def <span class="ident">make_all_agents_inaccessible</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.minibio"><code class="name flex">
<span>def <span class="ident">minibio</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a mini-biography of the TinyPerson.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def minibio(self):
    &#34;&#34;&#34;
    Returns a mini-biography of the TinyPerson.
    &#34;&#34;&#34;
    return f&#34;{self.name} is a {self._configuration[&#39;age&#39;]} year old {self._configuration[&#39;occupation&#39;]}, {self._configuration[&#39;nationality&#39;]}, currently living in {self._configuration[&#39;country_of_residence&#39;]}.&#34;</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.move_to"><code class="name flex">
<span>def <span class="ident">move_to</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.pop_actions_and_get_contents_for"><code class="name flex">
<span>def <span class="ident">pop_actions_and_get_contents_for</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.pop_and_display_latest_communications"><code class="name flex">
<span>def <span class="ident">pop_and_display_latest_communications</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Pops the latest communications and displays them.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def pop_and_display_latest_communications(self):
    &#34;&#34;&#34;
    Pops the latest communications and displays them.
    &#34;&#34;&#34;
    communications = self._displayed_communications_buffer
    self._displayed_communications_buffer = []

    for communication in communications:
        print(communication)

    return communications</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.pop_latest_actions"><code class="name flex">
<span>def <span class="ident">pop_latest_actions</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.pp_current_interactions"><code class="name flex">
<span>def <span class="ident">pp_current_interactions</span></span>(<span>self, simplified=True, skip_system=True, max_content_length=1024)</span>
</code></dt>
<dd>
<div class="desc"><p>Pretty prints the current messages.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def pp_current_interactions(
    self,
    simplified=True,
    skip_system=True,
    max_content_length=default[&#34;max_content_display_length&#34;],
):
    &#34;&#34;&#34;
    Pretty prints the current messages.
    &#34;&#34;&#34;
    print(
        self.pretty_current_interactions(
            simplified=simplified,
            skip_system=skip_system,
            max_content_length=max_content_length,
        )
    )</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.pretty_current_interactions"><code class="name flex">
<span>def <span class="ident">pretty_current_interactions</span></span>(<span>self, simplified=True, skip_system=True, max_content_length=1024, first_n=None, last_n=None, include_omission_info: bool = True)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a pretty, readable, string with the current messages.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def pretty_current_interactions(self, simplified=True, skip_system=True, max_content_length=default[&#34;max_content_display_length&#34;], first_n=None, last_n=None, include_omission_info:bool=True):
  &#34;&#34;&#34;
  Returns a pretty, readable, string with the current messages.
  &#34;&#34;&#34;
  lines = []
  for message in self.episodic_memory.retrieve(first_n=first_n, last_n=last_n, include_omission_info=include_omission_info):
    try:
        if not (skip_system and message[&#39;role&#39;] == &#39;system&#39;):
            msg_simplified_type = &#34;&#34;
            msg_simplified_content = &#34;&#34;
            msg_simplified_actor = &#34;&#34;

            lines.append(self._pretty_timestamp(message[&#39;role&#39;], message[&#39;simulation_timestamp&#39;]))

            if message[&#34;role&#34;] == &#34;system&#34;:
                msg_simplified_actor = &#34;SYSTEM&#34;
                msg_simplified_type = message[&#34;role&#34;]
                msg_simplified_content = message[&#34;content&#34;]

                lines.append(
                    f&#34;[dim] {msg_simplified_type}: {msg_simplified_content}[/]&#34;
                )

            elif message[&#34;role&#34;] == &#34;user&#34;:
                lines.append(
                    self._pretty_stimuli(
                        role=message[&#34;role&#34;],
                        content=message[&#34;content&#34;],
                        simplified=simplified,
                        max_content_length=max_content_length,
                    )
                )

            elif message[&#34;role&#34;] == &#34;assistant&#34;:
                lines.append(
                    self._pretty_action(
                        role=message[&#34;role&#34;],
                        content=message[&#34;content&#34;],
                        simplified=simplified,
                        max_content_length=max_content_length,
                    )
                )
            else:
                lines.append(f&#34;{message[&#39;role&#39;]}: {message[&#39;content&#39;]}&#34;)
    except:
        # print(f&#34;ERROR: {message}&#34;)
        continue

  return &#34;\n&#34;.join(lines)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.read_documents_from_folder"><code class="name flex">
<span>def <span class="ident">read_documents_from_folder</span></span>(<span>self, documents_path: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Reads documents from a directory and loads them into the semantic memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def read_documents_from_folder(self, documents_path:str):
    &#34;&#34;&#34;
    Reads documents from a directory and loads them into the semantic memory.
    &#34;&#34;&#34;
    logger.info(f&#34;Setting documents path to {documents_path} and loading documents.&#34;)

    self.semantic_memory.add_documents_path(documents_path)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.read_documents_from_web"><code class="name flex">
<span>def <span class="ident">read_documents_from_web</span></span>(<span>self, web_urls: list)</span>
</code></dt>
<dd>
<div class="desc"><p>Reads documents from web URLs and loads them into the semantic memory.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def read_documents_from_web(self, web_urls:list):
    &#34;&#34;&#34;
    Reads documents from web URLs and loads them into the semantic memory.
    &#34;&#34;&#34;
    logger.info(f&#34;Reading documents from the following web URLs: {web_urls}&#34;)

    self.semantic_memory.add_web_urls(web_urls)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.related_to"><code class="name flex">
<span>def <span class="ident">related_to</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.reset_prompt"><code class="name flex">
<span>def <span class="ident">reset_prompt</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def reset_prompt(self):

    # render the template with the current configuration
    self._init_system_message = self.generate_agent_prompt()

    # TODO actually, figure out another way to update agent state without &#34;changing history&#34;

    # reset system message
    self.current_messages = [
        {&#34;role&#34;: &#34;system&#34;, &#34;content&#34;: self._init_system_message}
    ]

    # sets up the actual interaction messages to use for prompting
    self.current_messages += self.episodic_memory.retrieve_recent()</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.save_spec"><code class="name flex">
<span>def <span class="ident">save_spec</span></span>(<span>self, path, include_mental_faculties=True, include_memory=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Saves the current configuration to a JSON file.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def save_spec(self, path, include_mental_faculties=True, include_memory=False):
    &#34;&#34;&#34;
    Saves the current configuration to a JSON file.
    &#34;&#34;&#34;
    
    suppress_attributes = []

    # should we include the memory?
    if not include_memory:
        suppress_attributes.append(&#34;episodic_memory&#34;)
        suppress_attributes.append(&#34;semantic_memory&#34;)

    # should we include the mental faculties?
    if not include_mental_faculties:
        suppress_attributes.append(&#34;_mental_faculties&#34;)

    self.to_json(suppress=suppress_attributes, file_path=path)</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.see"><code class="name flex">
<span>def <span class="ident">see</span></span>(<span>self, visual_description, source: Union[~Self, ForwardRef('TinyWorld')] = None, max_content_length=1024)</span>
</code></dt>
<dd>
<div class="desc"><p>Perceives a visual stimulus through a description and updates its internal cognitive state.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>visual_description</code></strong> :&ensp;<code>str</code></dt>
<dd>The description of the visual stimulus.</dd>
<dt><strong><code>source</code></strong> :&ensp;<code>AgentOrWorld</code>, optional</dt>
<dd>The source of the visual stimulus. Defaults to None.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def see(
    self,
    visual_description,
    source: AgentOrWorld = None,
    max_content_length=default[&#34;max_content_display_length&#34;],
):
    &#34;&#34;&#34;
    Perceives a visual stimulus through a description and updates its internal cognitive state.

    Args:
        visual_description (str): The description of the visual stimulus.
        source (AgentOrWorld, optional): The source of the visual stimulus. Defaults to None.
    &#34;&#34;&#34;
    return self._observe(
        stimulus={
            &#34;type&#34;: &#34;VISUAL&#34;,
            &#34;content&#34;: visual_description,
            &#34;source&#34;: name_or_empty(source),
        },
        max_content_length=max_content_length,
    )</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.see_and_act"><code class="name flex">
<span>def <span class="ident">see_and_act</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.socialize"><code class="name flex">
<span>def <span class="ident">socialize</span></span>(<span>self, social_description: str, source: Union[~Self, ForwardRef('TinyWorld')] = None, max_content_length=1024)</span>
</code></dt>
<dd>
<div class="desc"><p>Perceives a social stimulus through a description and updates its internal cognitive state.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>social_description</code></strong> :&ensp;<code>str</code></dt>
<dd>The description of the social stimulus.</dd>
<dt><strong><code>source</code></strong> :&ensp;<code>AgentOrWorld</code>, optional</dt>
<dd>The source of the social stimulus. Defaults to None.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def socialize(
    self,
    social_description: str,
    source: AgentOrWorld = None,
    max_content_length=default[&#34;max_content_display_length&#34;],
):
    &#34;&#34;&#34;
    Perceives a social stimulus through a description and updates its internal cognitive state.

    Args:
        social_description (str): The description of the social stimulus.
        source (AgentOrWorld, optional): The source of the social stimulus. Defaults to None.
    &#34;&#34;&#34;
    return self._observe(
        stimulus={
            &#34;type&#34;: &#34;SOCIAL&#34;,
            &#34;content&#34;: social_description,
            &#34;source&#34;: name_or_empty(source),
        },
        max_content_length=max_content_length,
    )</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.think"><code class="name flex">
<span>def <span class="ident">think</span></span>(<span>self, thought, max_content_length=1024)</span>
</code></dt>
<dd>
<div class="desc"><p>Forces the agent to think about something and updates its internal cognitive state.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def think(self, thought, max_content_length=default[&#34;max_content_display_length&#34;]):
    &#34;&#34;&#34;
    Forces the agent to think about something and updates its internal cognitive state.

    &#34;&#34;&#34;
    return self._observe(
        stimulus={
            &#34;type&#34;: &#34;THOUGHT&#34;,
            &#34;content&#34;: thought,
            &#34;source&#34;: name_or_empty(self),
        },
        max_content_length=max_content_length,
    )</code></pre>
</details>
</dd>
<dt id="tinytroupe.agent.TinyPerson.think_and_act"><code class="name flex">
<span>def <span class="ident">think_and_act</span></span>(<span>*args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def wrapper(*args, **kwargs):
    obj_under_transaction = args[0]
    simulation = current_simulation()
    obj_sim_id = obj_under_transaction.simulation_id if hasattr(obj_under_transaction, &#39;simulation_id&#39;) else None

    logger.debug(f&#34;-----------------------------------------&gt; Transaction: {func.__name__} with args {args[1:]} and kwargs {kwargs} under simulation {obj_sim_id}.&#34;)
    
    transaction = Transaction(obj_under_transaction, simulation, func, *args, **kwargs)
    result = transaction.execute()
    return result</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.utils.JsonSerializableRegistry.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="tinytroupe.agent.TinyToolUse"><code class="flex name class">
<span>class <span class="ident">TinyToolUse</span></span>
<span>(</span><span>tools: list)</span>
</code></dt>
<dd>
<div class="desc"><p>Allows the agent to use tools to accomplish tasks. Tool usage is one of the most important cognitive skills
humans and primates have as we know.</p>
<p>Initializes the mental faculty.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>name</code></strong> :&ensp;<code>str</code></dt>
<dd>The name of the mental faculty.</dd>
<dt><strong><code>requires_faculties</code></strong> :&ensp;<code>list</code></dt>
<dd>A list of mental faculties that this faculty requires to function properly.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class TinyToolUse(TinyMentalFaculty):
    &#34;&#34;&#34;
    Allows the agent to use tools to accomplish tasks. Tool usage is one of the most important cognitive skills
    humans and primates have as we know.
    &#34;&#34;&#34;

    def __init__(self, tools:list) -&gt; None:
        super().__init__(&#34;Tool Use&#34;)
    
        self.tools = tools
    
    def process_action(self, agent, action: dict) -&gt; bool:
        for tool in self.tools:
            if tool.process_action(agent, action):
                return True
        
        return False
    
    def actions_definitions_prompt(self) -&gt; str:
        # each tool should provide its own actions definitions prompt
        prompt = &#34;&#34;
        for tool in self.tools:
            prompt += tool.actions_definitions_prompt()
        
        return prompt
    
    def actions_constraints_prompt(self) -&gt; str:
        # each tool should provide its own actions constraints prompt
        prompt = &#34;&#34;
        for tool in self.tools:
            prompt += tool.actions_constraints_prompt()
        
        return prompt</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></li>
<li><a title="tinytroupe.utils.JsonSerializableRegistry" href="utils.html#tinytroupe.utils.JsonSerializableRegistry">JsonSerializableRegistry</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></b></code>:
<ul class="hlist">
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.from_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.from_json">from_json</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.process_action" href="#tinytroupe.agent.TinyMentalFaculty.process_action">process_action</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.to_json" href="utils.html#tinytroupe.utils.JsonSerializableRegistry.to_json">to_json</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="tinytroupe" href="index.html">tinytroupe</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="tinytroupe.agent.EpisodicMemory" href="#tinytroupe.agent.EpisodicMemory">EpisodicMemory</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.agent.EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO" href="#tinytroupe.agent.EpisodicMemory.MEMORY_BLOCK_OMISSION_INFO">MEMORY_BLOCK_OMISSION_INFO</a></code></li>
<li><code><a title="tinytroupe.agent.EpisodicMemory.count" href="#tinytroupe.agent.EpisodicMemory.count">count</a></code></li>
<li><code><a title="tinytroupe.agent.EpisodicMemory.retrieve_first" href="#tinytroupe.agent.EpisodicMemory.retrieve_first">retrieve_first</a></code></li>
<li><code><a title="tinytroupe.agent.EpisodicMemory.retrieve_last" href="#tinytroupe.agent.EpisodicMemory.retrieve_last">retrieve_last</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.agent.FilesAndWebGroundingFaculty" href="#tinytroupe.agent.FilesAndWebGroundingFaculty">FilesAndWebGroundingFaculty</a></code></h4>
</li>
<li>
<h4><code><a title="tinytroupe.agent.RecallFaculty" href="#tinytroupe.agent.RecallFaculty">RecallFaculty</a></code></h4>
</li>
<li>
<h4><code><a title="tinytroupe.agent.SemanticMemory" href="#tinytroupe.agent.SemanticMemory">SemanticMemory</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.agent.SemanticMemory.add_documents_path" href="#tinytroupe.agent.SemanticMemory.add_documents_path">add_documents_path</a></code></li>
<li><code><a title="tinytroupe.agent.SemanticMemory.add_documents_paths" href="#tinytroupe.agent.SemanticMemory.add_documents_paths">add_documents_paths</a></code></li>
<li><code><a title="tinytroupe.agent.SemanticMemory.add_web_url" href="#tinytroupe.agent.SemanticMemory.add_web_url">add_web_url</a></code></li>
<li><code><a title="tinytroupe.agent.SemanticMemory.add_web_urls" href="#tinytroupe.agent.SemanticMemory.add_web_urls">add_web_urls</a></code></li>
<li><code><a title="tinytroupe.agent.SemanticMemory.list_documents_names" href="#tinytroupe.agent.SemanticMemory.list_documents_names">list_documents_names</a></code></li>
<li><code><a title="tinytroupe.agent.SemanticMemory.retrieve_document_content_by_name" href="#tinytroupe.agent.SemanticMemory.retrieve_document_content_by_name">retrieve_document_content_by_name</a></code></li>
<li><code><a title="tinytroupe.agent.SemanticMemory.suppress_attributes_from_serialization" href="#tinytroupe.agent.SemanticMemory.suppress_attributes_from_serialization">suppress_attributes_from_serialization</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.agent.TinyMemory" href="#tinytroupe.agent.TinyMemory">TinyMemory</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve" href="#tinytroupe.agent.TinyMemory.retrieve">retrieve</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_all" href="#tinytroupe.agent.TinyMemory.retrieve_all">retrieve_all</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_recent" href="#tinytroupe.agent.TinyMemory.retrieve_recent">retrieve_recent</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.retrieve_relevant" href="#tinytroupe.agent.TinyMemory.retrieve_relevant">retrieve_relevant</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMemory.store" href="#tinytroupe.agent.TinyMemory.store">store</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.agent.TinyMentalFaculty" href="#tinytroupe.agent.TinyMentalFaculty">TinyMentalFaculty</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_constraints_prompt">actions_constraints_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt" href="#tinytroupe.agent.TinyMentalFaculty.actions_definitions_prompt">actions_definitions_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyMentalFaculty.process_action" href="#tinytroupe.agent.TinyMentalFaculty.process_action">process_action</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.agent.TinyPerson" href="#tinytroupe.agent.TinyPerson">TinyPerson</a></code></h4>
<ul class="">
<li><code><a title="tinytroupe.agent.TinyPerson.MAX_ACTIONS_BEFORE_DONE" href="#tinytroupe.agent.TinyPerson.MAX_ACTIONS_BEFORE_DONE">MAX_ACTIONS_BEFORE_DONE</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.PP_TEXT_WIDTH" href="#tinytroupe.agent.TinyPerson.PP_TEXT_WIDTH">PP_TEXT_WIDTH</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.act" href="#tinytroupe.agent.TinyPerson.act">act</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.add_agent" href="#tinytroupe.agent.TinyPerson.add_agent">add_agent</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.add_mental_faculties" href="#tinytroupe.agent.TinyPerson.add_mental_faculties">add_mental_faculties</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.add_mental_faculty" href="#tinytroupe.agent.TinyPerson.add_mental_faculty">add_mental_faculty</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.all_agents" href="#tinytroupe.agent.TinyPerson.all_agents">all_agents</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.change_context" href="#tinytroupe.agent.TinyPerson.change_context">change_context</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.clear_agents" href="#tinytroupe.agent.TinyPerson.clear_agents">clear_agents</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.clear_communications_buffer" href="#tinytroupe.agent.TinyPerson.clear_communications_buffer">clear_communications_buffer</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.clear_relationships" href="#tinytroupe.agent.TinyPerson.clear_relationships">clear_relationships</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.communication_display" href="#tinytroupe.agent.TinyPerson.communication_display">communication_display</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.communication_style" href="#tinytroupe.agent.TinyPerson.communication_style">communication_style</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.create_new_agent_from_current_spec" href="#tinytroupe.agent.TinyPerson.create_new_agent_from_current_spec">create_new_agent_from_current_spec</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.decode_complete_state" href="#tinytroupe.agent.TinyPerson.decode_complete_state">decode_complete_state</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.define" href="#tinytroupe.agent.TinyPerson.define">define</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.define_relationships" href="#tinytroupe.agent.TinyPerson.define_relationships">define_relationships</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.define_several" href="#tinytroupe.agent.TinyPerson.define_several">define_several</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.encode_complete_state" href="#tinytroupe.agent.TinyPerson.encode_complete_state">encode_complete_state</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.generate_agent_prompt" href="#tinytroupe.agent.TinyPerson.generate_agent_prompt">generate_agent_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.get" href="#tinytroupe.agent.TinyPerson.get">get</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.get_agent_by_name" href="#tinytroupe.agent.TinyPerson.get_agent_by_name">get_agent_by_name</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.has_agent" href="#tinytroupe.agent.TinyPerson.has_agent">has_agent</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.internalize_goal" href="#tinytroupe.agent.TinyPerson.internalize_goal">internalize_goal</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.iso_datetime" href="#tinytroupe.agent.TinyPerson.iso_datetime">iso_datetime</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.listen" href="#tinytroupe.agent.TinyPerson.listen">listen</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.listen_and_act" href="#tinytroupe.agent.TinyPerson.listen_and_act">listen_and_act</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.load_spec" href="#tinytroupe.agent.TinyPerson.load_spec">load_spec</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.make_agent_accessible" href="#tinytroupe.agent.TinyPerson.make_agent_accessible">make_agent_accessible</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.make_agent_inaccessible" href="#tinytroupe.agent.TinyPerson.make_agent_inaccessible">make_agent_inaccessible</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.make_all_agents_inaccessible" href="#tinytroupe.agent.TinyPerson.make_all_agents_inaccessible">make_all_agents_inaccessible</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.minibio" href="#tinytroupe.agent.TinyPerson.minibio">minibio</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.move_to" href="#tinytroupe.agent.TinyPerson.move_to">move_to</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.pop_actions_and_get_contents_for" href="#tinytroupe.agent.TinyPerson.pop_actions_and_get_contents_for">pop_actions_and_get_contents_for</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.pop_and_display_latest_communications" href="#tinytroupe.agent.TinyPerson.pop_and_display_latest_communications">pop_and_display_latest_communications</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.pop_latest_actions" href="#tinytroupe.agent.TinyPerson.pop_latest_actions">pop_latest_actions</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.pp_current_interactions" href="#tinytroupe.agent.TinyPerson.pp_current_interactions">pp_current_interactions</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.pretty_current_interactions" href="#tinytroupe.agent.TinyPerson.pretty_current_interactions">pretty_current_interactions</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.read_documents_from_folder" href="#tinytroupe.agent.TinyPerson.read_documents_from_folder">read_documents_from_folder</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.read_documents_from_web" href="#tinytroupe.agent.TinyPerson.read_documents_from_web">read_documents_from_web</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.related_to" href="#tinytroupe.agent.TinyPerson.related_to">related_to</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.reset_prompt" href="#tinytroupe.agent.TinyPerson.reset_prompt">reset_prompt</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.save_spec" href="#tinytroupe.agent.TinyPerson.save_spec">save_spec</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.see" href="#tinytroupe.agent.TinyPerson.see">see</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.see_and_act" href="#tinytroupe.agent.TinyPerson.see_and_act">see_and_act</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.serializable_attributes" href="#tinytroupe.agent.TinyPerson.serializable_attributes">serializable_attributes</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.set_simulation_for_free_agents" href="#tinytroupe.agent.TinyPerson.set_simulation_for_free_agents">set_simulation_for_free_agents</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.socialize" href="#tinytroupe.agent.TinyPerson.socialize">socialize</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.think" href="#tinytroupe.agent.TinyPerson.think">think</a></code></li>
<li><code><a title="tinytroupe.agent.TinyPerson.think_and_act" href="#tinytroupe.agent.TinyPerson.think_and_act">think_and_act</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="tinytroupe.agent.TinyToolUse" href="#tinytroupe.agent.TinyToolUse">TinyToolUse</a></code></h4>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>